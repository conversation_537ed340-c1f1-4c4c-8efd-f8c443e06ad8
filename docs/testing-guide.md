# Testing Guide

This document provides comprehensive guidelines for testing the ATS Frontend Application, including strategies, best practices, and coverage requirements.

## 📋 Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Testing Tools](#testing-tools)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [Component Testing](#component-testing)
6. [API Testing](#api-testing)
7. [Coverage Requirements](#coverage-requirements)
8. [Test Organization](#test-organization)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## 🎯 Testing Strategy

### Testing Pyramid

```
    E2E Tests (Few)
        /\
       /  \
   Integration Tests (Some)
      /    \
     /      \
Unit Tests (Many)
```

### Testing Levels

1. **Unit Tests** (70% of tests)
   - Individual components
   - Utility functions
   - Custom hooks
   - Redux reducers

2. **Integration Tests** (20% of tests)
   - Component interactions
   - API integration
   - State management
   - Route testing

3. **E2E Tests** (10% of tests)
   - Critical user journeys
   - Complete workflows
   - Cross-browser testing

## 🛠️ Testing Tools

### Primary Tools

- **Jest**: Testing framework
- **React Testing Library**: Component testing
- **@testing-library/jest-dom**: Custom matchers
- **@testing-library/user-event**: User interactions
- **jest-environment-jsdom**: DOM environment

### Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js', '<rootDir>/test/setup.js'],
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
    '^components/(.*)$': '<rootDir>/src/components/$1',
    '^pages/(.*)$': '<rootDir>/src/pages/$1',
    '^utils/(.*)$': '<rootDir>/src/utils/$1',
    '^hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^api/(.*)$': '<rootDir>/src/api/$1',
    '^themes/(.*)$': '<rootDir>/src/themes/$1',
    '^menu-items/(.*)$': '<rootDir>/src/menu-items/$1',
    '^custom-components/(.*)$': '<rootDir>/src/custom-components/$1',
    '^constants/(.*)$': '<rootDir>/src/constants/$1',
    '^store/(.*)$': '<rootDir>/src/store/$1',
    '^layout/(.*)$': '<rootDir>/src/layout/$1',
    '^routes/(.*)$': '<rootDir>/src/routes/$1',
    '^sections/(.*)$': '<rootDir>/src/sections/$1',
    '^profile-menu/(.*)$': '<rootDir>/src/profile-menu/$1',
    '^data/(.*)$': '<rootDir>/src/data/$1',
    '^assets/(.*)$': '<rootDir>/src/assets/$1'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.jsx',
    '!src/reportWebVitals.js',
    '!src/setupTests.js'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',
  testTimeout: 10000,
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
};
```

## 🧪 Unit Testing

### Component Testing

```javascript
// UserProfile.test.jsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import UserProfile from './UserProfile';

describe('UserProfile', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
  };

  const mockOnUpdate = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders user information correctly', () => {
      render(<UserProfile user={mockUser} onUpdate={mockOnUpdate} />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByAltText('User avatar')).toBeInTheDocument();
    });

    it('renders loading state when user is null', () => {
      render(<UserProfile user={null} onUpdate={mockOnUpdate} />);
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('renders error state when user is invalid', () => {
      render(<UserProfile user={{}} onUpdate={mockOnUpdate} />);
      
      expect(screen.getByText('Invalid user data')).toBeInTheDocument();
    });
  });

  describe('Interactions', () => {
    it('calls onUpdate when edit button is clicked', () => {
      render(<UserProfile user={mockUser} onUpdate={mockOnUpdate} />);
      
      const editButton = screen.getByText('Edit');
      fireEvent.click(editButton);
      
      expect(mockOnUpdate).toHaveBeenCalledWith(mockUser);
    });

    it('calls onUpdate when delete button is clicked', () => {
      render(<UserProfile user={mockUser} onUpdate={mockOnUpdate} />);
      
      const deleteButton = screen.getByText('Delete');
      fireEvent.click(deleteButton);
      
      expect(mockOnUpdate).toHaveBeenCalledWith({ action: 'delete', user: mockUser });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<UserProfile user={mockUser} onUpdate={mockOnUpdate} />);
      
      expect(screen.getByLabelText('Edit user profile')).toBeInTheDocument();
      expect(screen.getByLabelText('Delete user')).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<UserProfile user={mockUser} onUpdate={mockOnUpdate} />);
      
      const editButton = screen.getByText('Edit');
      editButton.focus();
      
      expect(editButton).toHaveFocus();
    });
  });
});
```

### Custom Hook Testing

```javascript
// useUser.test.js
import { renderHook, act } from '@testing-library/react';
import { useUser } from './useUser';

// Mock API function
jest.mock('../api/userApi', () => ({
  fetchUser: jest.fn(),
}));

import { fetchUser } from '../api/userApi';

describe('useUser', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns initial state', () => {
    const { result } = renderHook(() => useUser('1'));
    
    expect(result.current.user).toBeNull();
    expect(result.current.loading).toBe(true);
    expect(result.current.error).toBeNull();
  });

  it('fetches user data successfully', async () => {
    fetchUser.mockResolvedValue(mockUser);
    
    const { result, waitForNextUpdate } = renderHook(() => useUser('1'));
    
    await waitForNextUpdate();
    
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(fetchUser).toHaveBeenCalledWith('1');
  });

  it('handles fetch error', async () => {
    const error = new Error('Failed to fetch user');
    fetchUser.mockRejectedValue(error);
    
    const { result, waitForNextUpdate } = renderHook(() => useUser('1'));
    
    await waitForNextUpdate();
    
    expect(result.current.user).toBeNull();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toEqual(error);
  });

  it('does not fetch when userId is null', () => {
    renderHook(() => useUser(null));
    
    expect(fetchUser).not.toHaveBeenCalled();
  });
});
```

### Utility Function Testing

```javascript
// formatDate.test.js
import { formatDate, formatDateTime, formatRelativeTime } from './formatDate';

describe('Date formatting utilities', () => {
  const testDate = new Date('2023-01-15T10:30:00Z');

  describe('formatDate', () => {
    it('formats date correctly', () => {
      const result = formatDate(testDate);
      expect(result).toBe('January 15, 2023');
    });

    it('handles null input', () => {
      const result = formatDate(null);
      expect(result).toBe('N/A');
    });

    it('handles invalid date', () => {
      const result = formatDate('invalid-date');
      expect(result).toBe('Invalid Date');
    });
  });

  describe('formatDateTime', () => {
    it('formats date and time correctly', () => {
      const result = formatDateTime(testDate);
      expect(result).toBe('January 15, 2023 at 10:30 AM');
    });
  });

  describe('formatRelativeTime', () => {
    it('formats relative time correctly', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      const result = formatRelativeTime(oneHourAgo);
      expect(result).toBe('1 hour ago');
    });
  });
});
```

## 🔗 Integration Testing

### Component Integration

```javascript
// UserList.test.jsx
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import userReducer from '../store/reducers/user';
import UserList from './UserList';

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      user: userReducer,
    },
    preloadedState: initialState,
  });
};

describe('UserList Integration', () => {
  const mockUsers = [
    { id: '1', name: 'John Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
  ];

  it('loads and displays users from Redux store', async () => {
    const store = createTestStore({
      user: {
        users: mockUsers,
        loading: false,
        error: null,
      },
    });

    render(
      <Provider store={store}>
        <UserList />
      </Provider>
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    const store = createTestStore({
      user: {
        users: [],
        loading: true,
        error: null,
      },
    });

    render(
      <Provider store={store}>
        <UserList />
      </Provider>
    );

    expect(screen.getByText('Loading users...')).toBeInTheDocument();
  });

  it('handles error state', () => {
    const store = createTestStore({
      user: {
        users: [],
        loading: false,
        error: 'Failed to load users',
      },
    });

    render(
      <Provider store={store}>
        <UserList />
      </Provider>
    );

    expect(screen.getByText('Error: Failed to load users')).toBeInTheDocument();
  });
});
```

### API Integration Testing

```javascript
// userApi.test.js
import { fetchUsers, createUser, updateUser, deleteUser } from './userApi';

// Mock fetch
global.fetch = jest.fn();

describe('User API Integration', () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  describe('fetchUsers', () => {
    it('fetches users successfully', async () => {
      const mockUsers = [
        { id: '1', name: 'John Doe' },
        { id: '2', name: 'Jane Smith' },
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockUsers,
      });

      const result = await fetchUsers();
      
      expect(result).toEqual(mockUsers);
      expect(fetch).toHaveBeenCalledWith('/api/users', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    it('handles API error', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      await expect(fetchUsers()).rejects.toThrow('Failed to fetch users');
    });
  });

  describe('createUser', () => {
    it('creates user successfully', async () => {
      const newUser = { name: 'John Doe', email: '<EMAIL>' };
      const createdUser = { id: '1', ...newUser };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => createdUser,
      });

      const result = await createUser(newUser);
      
      expect(result).toEqual(createdUser);
      expect(fetch).toHaveBeenCalledWith('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      });
    });
  });
});
```

## 🧩 Component Testing

### Form Testing

```javascript
// UserForm.test.jsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UserForm from './UserForm';

describe('UserForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders form fields correctly', () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByRole('button', { name: 'Submit' });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
    
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    const nameInput = screen.getByLabelText('Name');
    const emailInput = screen.getByLabelText('Email');
    const submitButton = screen.getByRole('button', { name: 'Submit' });
    
    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });
  });

  it('validates email format', async () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    const emailInput = screen.getByLabelText('Email');
    await userEvent.type(emailInput, 'invalid-email');
    
    fireEvent.blur(emailInput);
    
    await waitFor(() => {
      expect(screen.getByText('Invalid email format')).toBeInTheDocument();
    });
  });
});
```

### Modal/Dialog Testing

```javascript
// ConfirmDialog.test.jsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ConfirmDialog from './ConfirmDialog';

describe('ConfirmDialog', () => {
  const mockOnConfirm = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders dialog with correct content', () => {
    render(
      <ConfirmDialog
        open={true}
        title="Delete User"
        message="Are you sure you want to delete this user?"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );
    
    expect(screen.getByText('Delete User')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete this user?')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Confirm' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
  });

  it('calls onConfirm when confirm button is clicked', () => {
    render(
      <ConfirmDialog
        open={true}
        title="Delete User"
        message="Are you sure?"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );
    
    const confirmButton = screen.getByRole('button', { name: 'Confirm' });
    fireEvent.click(confirmButton);
    
    expect(mockOnConfirm).toHaveBeenCalled();
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <ConfirmDialog
        open={true}
        title="Delete User"
        message="Are you sure?"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );
    
    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    fireEvent.click(cancelButton);
    
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('does not render when open is false', () => {
    render(
      <ConfirmDialog
        open={false}
        title="Delete User"
        message="Are you sure?"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );
    
    expect(screen.queryByText('Delete User')).not.toBeInTheDocument();
  });
});
```

## 🌐 API Testing

### Mock Service Worker

```javascript
// setupTests.js
import { server } from './mocks/server';

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

```javascript
// mocks/handlers.js
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json([
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
      ])
    );
  }),

  rest.post('/api/users', (req, res, ctx) => {
    const { name, email } = req.body;
    return res(
      ctx.status(201),
      ctx.json({ id: '3', name, email })
    );
  }),

  rest.put('/api/users/:id', (req, res, ctx) => {
    const { id } = req.params;
    const { name, email } = req.body;
    return res(
      ctx.status(200),
      ctx.json({ id, name, email })
    );
  }),

  rest.delete('/api/users/:id', (req, res, ctx) => {
    return res(ctx.status(204));
  }),
];
```

### API Integration Tests

```javascript
// userApi.integration.test.js
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { fetchUsers, createUser, updateUser, deleteUser } from './userApi';

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.json([
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
      ])
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('User API Integration', () => {
  it('fetches users successfully', async () => {
    const users = await fetchUsers();
    
    expect(users).toHaveLength(1);
    expect(users[0]).toEqual({
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
    });
  });

  it('handles server error', async () => {
    server.use(
      rest.get('/api/users', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );

    await expect(fetchUsers()).rejects.toThrow();
  });
});
```

## 📊 Coverage Requirements

### Coverage Targets

- **Overall Coverage**: 80% minimum
- **Critical Paths**: 100% coverage
- **Business Logic**: 90% coverage
- **UI Components**: 70% coverage
- **Utility Functions**: 95% coverage

### Coverage Configuration

```javascript
// jest.config.js
module.exports = {
  // ... other config
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.jsx',
    '!src/reportWebVitals.js',
    '!src/setupTests.js',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/*.spec.{js,jsx,ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/utils/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    './src/components/': {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',
};
```

## 📁 Test Organization

### Directory Structure

```
test/
├── unit/                    # Unit tests
│   ├── components/         # Component tests
│   ├── hooks/             # Custom hook tests
│   ├── utils/             # Utility function tests
│   └── reducers/          # Redux reducer tests
├── integration/            # Integration tests
│   ├── api/               # API integration tests
│   ├── components/        # Component integration tests
│   └── workflows/         # User workflow tests
├── e2e/                   # End-to-end tests
│   ├── auth/              # Authentication flows
│   ├── user-management/   # User management flows
│   └── admin/             # Admin workflows
├── mocks/                 # Mock data and handlers
│   ├── handlers.js        # MSW handlers
│   ├── server.js          # MSW server setup
│   └── data/              # Mock data files
└── setup.js               # Test setup configuration
```

### Test File Naming

```bash
# ✅ Good - Descriptive test file names
UserProfile.test.jsx
useUser.test.js
formatDate.test.js
userApi.integration.test.js

# ❌ Bad - Unclear test file names
test.js
spec.js
component.test.js
```

## ✅ Best Practices

### 1. Test Structure (AAA Pattern)

```javascript
describe('ComponentName', () => {
  // Arrange - Setup test data and mocks
  const mockData = { /* test data */ };
  const mockFunction = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Feature', () => {
    it('should behave correctly', () => {
      // Arrange - Prepare test conditions
      render(<Component data={mockData} onAction={mockFunction} />);
      
      // Act - Perform the action being tested
      fireEvent.click(screen.getByText('Button'));
      
      // Assert - Verify the expected outcome
      expect(mockFunction).toHaveBeenCalledWith(expectedValue);
    });
  });
});
```

### 2. Meaningful Test Names

```javascript
// ✅ Good - Descriptive test names
it('should display user name when user data is provided');
it('should show loading spinner while fetching data');
it('should call onDelete when delete button is clicked');
it('should validate email format and show error for invalid email');

// ❌ Bad - Unclear test names
it('should work');
it('should handle data');
it('should be correct');
```

### 3. Mock Management

```javascript
// ✅ Good - Proper mock setup and cleanup
describe('Component', () => {
  const mockApi = {
    fetchData: jest.fn(),
    updateData: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockApi.fetchData.mockResolvedValue({ data: 'test' });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
});
```

### 4. Async Testing

```javascript
// ✅ Good - Proper async test handling
it('should load data asynchronously', async () => {
  render(<AsyncComponent />);
  
  // Wait for loading to complete
  await waitFor(() => {
    expect(screen.getByText('Data loaded')).toBeInTheDocument();
  });
  
  // Verify final state
  expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
});
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Test Environment Issues

```javascript
// ✅ Good - Proper test environment setup
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
  },
};
```

#### 2. Mock Issues

```javascript
// ✅ Good - Proper module mocking
jest.mock('../api/userApi', () => ({
  fetchUser: jest.fn(),
  createUser: jest.fn(),
}));

// ✅ Good - Mock implementation
import { fetchUser } from '../api/userApi';

fetchUser.mockResolvedValue({ id: '1', name: 'John' });
```

#### 3. Async Test Issues

```javascript
// ✅ Good - Proper async test handling
it('should handle async operations', async () => {
  render(<AsyncComponent />);
  
  // Wait for async operation
  await waitFor(() => {
    expect(screen.getByText('Result')).toBeInTheDocument();
  });
  
  // Additional assertions
  expect(screen.queryByText('Loading')).not.toBeInTheDocument();
});
```

### Debugging Tests

```javascript
// ✅ Good - Debug test output
it('should work correctly', () => {
  const { debug } = render(<Component />);
  
  // Log the rendered HTML
  debug();
  
  // Or log specific elements
  console.log(screen.getByText('Button'));
});
```

## 📈 Performance Testing

### Component Performance

```javascript
// Performance test example
import { render } from '@testing-library/react';
import { performance } from 'perf_hooks';

it('should render within performance budget', () => {
  const start = performance.now();
  
  render(<LargeComponent data={largeDataset} />);
  
  const end = performance.now();
  const renderTime = end - start;
  
  expect(renderTime).toBeLessThan(100); // 100ms budget
});
```

This comprehensive testing guide ensures high-quality, maintainable tests that provide confidence in the application's reliability and functionality. 