# ATS Frontend Application Documentation

Welcome to the ATS (Applicant Tracking System) Frontend Application documentation.

## 📚 Documentation Index

- **[SonarQube Setup & Usage](./sonarqube-setup.md)** - Complete guide for SonarQube configuration and analysis
- **[Semgrep Security Scanning](./semgrep-scan.md)** - Comprehensive security scanning with Semgrep
- **[Project Structure](./project-structure.md)** - Overview of the project architecture and file organization
- **[Development Guidelines](./development-guidelines.md)** - Coding standards and best practices
- **[Testing Guide](./testing-guide.md)** - Testing strategies and coverage requirements
- **[Deployment Guide](./deployment-guide.md)** - Deployment procedures and environment setup

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- Docker and Docker Compose
- SonarQube Scanner
- Python 3.7+ (for Semgrep)

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run start:dev

# Run tests
npm test

# Run SonarQube analysis
docker-compose -f docker-compose.sonarqube.yml up -d
npm run test:coverage
npx sonar-scanner

# Run Semgrep security scan
semgrep --config=auto
```

## 📋 Project Overview

This is a React-based Applicant Tracking System frontend application built with:
- **React 18** with Vite
- **Material-UI** for components
- **Jest** for testing
- **SonarQube** for code quality analysis
- **Semgrep** for security scanning
- **Docker** for containerization

## 🎯 Key Features

- **Modular Architecture**: Well-organized component structure
- **Quality Assurance**: Automated code quality checks with SonarQube
- **Security Scanning**: Comprehensive security analysis with Semgrep
- **Testing Coverage**: Comprehensive test suite with 80% coverage requirement
- **Responsive Design**: Mobile-first approach with Material-UI
- **Type Safety**: PropTypes and ESLint for code quality

## 🔧 Configuration

The project includes several configuration files:
- `sonar-project.properties` - SonarQube analysis settings
- `.semgrep.yml` - Semgrep security scanning rules
- `jest.config.js` - Testing configuration
- `docker-compose.sonarqube.yml` - SonarQube Docker setup
- `.gitignore` - Version control exclusions

## 📊 Quality Metrics

- **Code Coverage**: 80% minimum requirement
- **Code Quality**: SonarQube analysis with quality gates
- **Security**: Semgrep security scanning and vulnerability detection
- **Performance**: Optimized bundle size and loading times
- **Accessibility**: WCAG 2.1 compliance

## 🤝 Contributing

Please refer to the [Development Guidelines](./development-guidelines.md) for contribution standards and procedures.

## 📞 Support

For technical support or questions, please refer to the relevant documentation sections or contact the development team. 