# Deployment Guide

This document provides comprehensive instructions for deploying the ATS Frontend Application across different environments and platforms.

## 📋 Table of Contents

1. [Environment Setup](#environment-setup)
2. [Build Process](#build-process)
3. [Deployment Strategies](#deployment-strategies)
4. [Environment Configuration](#environment-configuration)
5. [CI/CD Pipeline](#cicd-pipeline)
6. [Monitoring & Logging](#monitoring--logging)
7. [Performance Optimization](#performance-optimization)
8. [Security Considerations](#security-considerations)
9. [Troubleshooting](#troubleshooting)

## 🌍 Environment Setup

### Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn** package manager
- **Docker** (for containerized deployment)
- **Git** for version control
- **CI/CD platform** (GitHub Actions, GitLab CI, etc.)

### Environment Variables

Create environment-specific configuration files:

```bash
# .env.development
VITE_APP_API_URL=http://localhost:8000
VITE_APP_ENV=development
VITE_APP_DEBUG=true
VITE_APP_SONAR_TOKEN=sqp_f182504535bb863594d1fe3057b373f72a4664d2

# .env.beta
VITE_APP_API_URL=https://beta-api.example.com
VITE_APP_ENV=beta
VITE_APP_DEBUG=false
VITE_APP_SONAR_TOKEN=sqp_f182504535bb863594d1fe3057b373f72a4664d2

# .env.production
VITE_APP_API_URL=https://api.example.com
VITE_APP_ENV=production
VITE_APP_DEBUG=false
VITE_APP_SONAR_TOKEN=sqp_f182504535bb863594d1fe3057b373f72a4664d2
```

### Environment Configuration

```javascript
// src/config/environment.js
const environment = {
  development: {
    apiUrl: 'http://localhost:8000',
    debug: true,
    logLevel: 'debug',
  },
  beta: {
    apiUrl: 'https://beta-api.example.com',
    debug: false,
    logLevel: 'info',
  },
  production: {
    apiUrl: 'https://api.example.com',
    debug: false,
    logLevel: 'error',
  },
};

export const getEnvironmentConfig = () => {
  const env = import.meta.env.VITE_APP_ENV || 'development';
  return environment[env] || environment.development;
};
```

## 🔨 Build Process

### Local Build

```bash
# Install dependencies
npm install

# Build for development
npm run build:dev

# Build for beta
npm run build:beta

# Build for production
npm run build:prod

# Preview build
npm run preview
```

### Build Scripts

```json
{
  "scripts": {
    "build:dev": "env-cmd -f .env.development vite build",
    "build:beta": "env-cmd -f .env.beta vite build",
    "build:prod": "env-cmd -f .env.production vite build",
    "build:analyze": "npm run build:prod && npx vite-bundle-analyzer dist",
    "build:docker": "docker build -t ats-frontend .",
    "deploy:dev": "npm run build:dev && npm run deploy:upload",
    "deploy:beta": "npm run build:beta && npm run deploy:upload",
    "deploy:prod": "npm run build:prod && npm run deploy:upload"
  }
}
```

### Vite Configuration

```javascript
// vite.config.js
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import jsconfigPaths from 'vite-jsconfig-paths';

export default defineConfig(({ mode }) => ({
  plugins: [react(), jsconfigPaths()],
  
  build: {
    outDir: 'dist',
    sourcemap: mode === 'development',
    minify: mode === 'production',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          utils: ['lodash', 'axios'],
        },
      },
    },
  },
  
  server: {
    port: 3000,
    host: true,
  },
  
  preview: {
    port: 4173,
    host: true,
  },
}));
```

## 🚀 Deployment Strategies

### 1. Static Hosting (Recommended)

#### Netlify Deployment

```yaml
# netlify.toml
[build]
  command = "npm run build:prod"
  publish = "dist"

[build.environment]
  NODE_VERSION = "16"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

#### Vercel Deployment

```json
// vercel.json
{
  "buildCommand": "npm run build:prod",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### 2. Docker Deployment

#### Dockerfile

```dockerfile
# Dockerfile
FROM node:16-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY yarn.lock ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build:prod

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API proxy (if needed)
        location /api/ {
            proxy_pass http://backend:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

#### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  sonarqube:
    image: sonarqube:community
    ports:
      - "9000:9000"
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions

volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
```

### 3. Cloud Platform Deployment

#### AWS S3 + CloudFront

```bash
# Deploy to S3
aws s3 sync dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

#### Google Cloud Storage

```bash
# Deploy to GCS
gsutil -m rsync -r -d dist/ gs://your-bucket-name

# Make bucket public
gsutil iam ch allUsers:objectViewer gs://your-bucket-name
```

## ⚙️ Environment Configuration

### Environment-Specific Builds

```javascript
// scripts/build.js
const { execSync } = require('child_process');
const fs = require('fs');

const environment = process.argv[2] || 'development';

// Load environment variables
require('dotenv').config({ path: `.env.${environment}` });

// Update build configuration
const updateBuildConfig = () => {
  const config = {
    apiUrl: process.env.VITE_APP_API_URL,
    environment: environment,
    debug: process.env.VITE_APP_DEBUG === 'true',
    version: process.env.npm_package_version,
    buildTime: new Date().toISOString(),
  };

  fs.writeFileSync(
    'src/config/build-config.json',
    JSON.stringify(config, null, 2)
  );
};

// Run build
const runBuild = () => {
  try {
    updateBuildConfig();
    execSync(`npm run build:${environment}`, { stdio: 'inherit' });
    console.log(`✅ Build completed for ${environment} environment`);
  } catch (error) {
    console.error(`❌ Build failed for ${environment} environment:`, error);
    process.exit(1);
  }
};

runBuild();
```

### Runtime Configuration

```javascript
// src/config/runtime.js
export const getRuntimeConfig = () => {
  // Load build-time configuration
  const buildConfig = require('./build-config.json');
  
  // Override with runtime environment variables
  return {
    ...buildConfig,
    apiUrl: import.meta.env.VITE_APP_API_URL || buildConfig.apiUrl,
    environment: import.meta.env.VITE_APP_ENV || buildConfig.environment,
    debug: import.meta.env.VITE_APP_DEBUG === 'true' || buildConfig.debug,
  };
};
```

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run SonarQube analysis
      run: npm run sonar:mock
    
    - name: Build application
      run: npm run build:prod

  deploy-dev:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build for development
      run: npm run build:dev
      env:
        VITE_APP_API_URL: ${{ secrets.DEV_API_URL }}
    
    - name: Deploy to development
      run: |
        # Deploy to development environment
        echo "Deploying to development..."

  deploy-prod:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build for production
      run: npm run build:prod
      env:
        VITE_APP_API_URL: ${{ secrets.PROD_API_URL }}
    
    - name: Deploy to production
      run: |
        # Deploy to production environment
        echo "Deploying to production..."
```

### GitLab CI

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "16"

cache:
  paths:
    - node_modules/

test:
  stage: test
  image: node:$NODE_VERSION
  script:
    - npm ci
    - npm test
    - npm run sonar:mock
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'

build-dev:
  stage: build
  image: node:$NODE_VERSION
  script:
    - npm ci
    - npm run build:dev
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  only:
    - develop

build-prod:
  stage: build
  image: node:$NODE_VERSION
  script:
    - npm ci
    - npm run build:prod
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
  only:
    - main

deploy-dev:
  stage: deploy
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - curl -X POST $DEV_DEPLOY_WEBHOOK
  only:
    - develop

deploy-prod:
  stage: deploy
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - curl -X POST $PROD_DEPLOY_WEBHOOK
  only:
    - main
  when: manual
```

## 📊 Monitoring & Logging

### Application Monitoring

```javascript
// src/utils/monitoring.js
export const initializeMonitoring = () => {
  // Initialize error tracking
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // Send to monitoring service
  });

  // Initialize performance monitoring
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0];
      console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart);
    });
  }
};

export const logError = (error, context = {}) => {
  console.error('Application error:', error, context);
  // Send to error tracking service
};

export const logEvent = (eventName, data = {}) => {
  console.log('Event:', eventName, data);
  // Send to analytics service
};
```

### Health Checks

```javascript
// src/utils/healthCheck.js
export const performHealthCheck = async () => {
  try {
    const response = await fetch('/api/health');
    const data = await response.json();
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      api: data.status,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
    };
  }
};
```

## ⚡ Performance Optimization

### Build Optimization

```javascript
// vite.config.js - Performance optimizations
export default defineConfig({
  build: {
    // Enable tree shaking
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          utils: ['lodash', 'axios'],
        },
      },
    },
    
    // Enable compression
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    
    // Generate source maps for debugging
    sourcemap: false,
  },
  
  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom', '@mui/material'],
  },
});
```

### Runtime Optimization

```javascript
// src/utils/performance.js
export const lazyLoadComponent = (importFunc) => {
  return React.lazy(importFunc);
};

export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
```

## 🔒 Security Considerations

### Security Headers

```nginx
# nginx.conf - Security headers
add_header X-Frame-Options "DENY" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
```

### Environment Variable Security

```bash
# .env.example (template)
VITE_APP_API_URL=https://api.example.com
VITE_APP_ENV=production
VITE_APP_DEBUG=false

# Never commit actual secrets
# VITE_APP_SECRET_KEY=your-secret-key
```

### Content Security Policy

```html
<!-- index.html -->
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:;">
```

## 🛠️ Troubleshooting

### Common Deployment Issues

#### 1. Build Failures

```bash
# Check Node.js version
node --version

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for syntax errors
npm run lint

# Run tests
npm test
```

#### 2. Environment Variable Issues

```bash
# Verify environment variables
echo $VITE_APP_API_URL

# Check build output
npm run build:prod
ls -la dist/
```

#### 3. Routing Issues

```nginx
# nginx.conf - Handle client-side routing
location / {
    try_files $uri $uri/ /index.html;
}
```

#### 4. Performance Issues

```bash
# Analyze bundle size
npm run build:analyze

# Check for large dependencies
npm ls --depth=0

# Optimize images
npm run optimize-images
```

### Debug Commands

```bash
# Development server
npm run dev

# Build and preview
npm run build:prod && npm run preview

# Docker build
docker build -t ats-frontend .

# Docker run
docker run -p 80:80 ats-frontend

# Check logs
docker logs <container-id>
```

### Monitoring Commands

```bash
# Check application health
curl http://localhost/api/health

# Monitor performance
npm run build:analyze

# Check bundle size
npx vite-bundle-analyzer dist

# Monitor network requests
# Use browser DevTools Network tab
```

This comprehensive deployment guide ensures reliable, secure, and performant deployments across different environments and platforms. 