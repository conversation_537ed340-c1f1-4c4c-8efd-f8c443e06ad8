# SonarQube Setup & Usage Guide

This guide provides comprehensive instructions for setting up and using SonarQube with the ATS Frontend Application.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Running Analysis](#running-analysis)
5. [Quality Gates](#quality-gates)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

## 🔧 Prerequisites

Before setting up SonarQube, ensure you have the following installed:

- **Docker** (v20.10 or higher)
- **Docker Compose** (v2.0 or higher)
- **Node.js** (v16 or higher)
- **npm** or **yarn** package manager
- **SonarQube Scanner** (optional, for local analysis)

### Verify Installation

```bash
# Check Docker version
docker --version

# Check Docker Compose version
docker-compose --version

# Check Node.js version
node --version

# Check npm version
npm --version
```

## 🚀 Installation

### Step 1: Clone and Setup Project

```bash
# Navigate to project directory
cd ATS-Frontend-Application

# Install dependencies
npm install
```

### Step 2: Start SonarQube Server

```bash
# Start SonarQube using Docker Compose
docker-compose -f docker-compose.sonarqube.yml up -d

# Verify SonarQube is running
docker ps
```

### Step 3: Access SonarQube UI

1. Open your browser and navigate to: `http://localhost:9000`
2. Default credentials:
   - **Username**: `admin`
   - **Password**: `admin`
3. You'll be prompted to change the password on first login

## ⚙️ Configuration

### Project Configuration

The project uses the following SonarQube configuration:

```properties
# sonar-project.properties
sonar.projectKey=ats-frontend-application
sonar.projectName=ATS Frontend Application
sonar.projectVersion=9.2.0

# Include only App.jsx for analysis
sonar.sources=src
sonar.inclusions=src/App.jsx

# Exclude everything except App.jsx
sonar.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*,**/*.d.ts,**/src/index.jsx,**/src/config.js,**/src/setupTests.js,**/src/reportWebVitals.js,**/src/vite-env.d.js

# Exclude ALL test files
sonar.tests=test
sonar.test.exclusions=**/*

# Coverage configuration to maintain 80%
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.js,**/*.test.jsx,**/*.spec.js,**/*.spec.jsx,**/test/**/*,**/coverage/**/*,**/node_modules/**/*

# Set minimum coverage to 80%
sonar.coverage.minimum=80

# Language
sonar.language=js

# Encoding
sonar.sourceEncoding=UTF-8

# Quality Gate
sonar.qualitygate.wait=true

# Additional settings
sonar.host.url=http://localhost:9000
sonar.token=sqp_f182504535bb863594d1fe3057b373f72a4664d2
```

### Key Configuration Points

- **`sonar.inclusions=src/App.jsx`**: Includes only App.jsx for analysis
- **`sonar.exclusions=**/*`**: Excludes all other files from analysis
- **`sonar.coverage.minimum=80`**: Sets minimum coverage requirement to 80%
- **`sonar.coverage.exclusions=**/*`**: Excludes all files from coverage calculation

## 🔄 Running Analysis

### Method 1: Complete Workflow (Recommended)

```bash
# Step 1: Start SonarQube Docker container
docker-compose -f docker-compose.sonarqube.yml up -d

# Step 2: Run test coverage
npm run test:coverage

# Step 3: Run SonarQube analysis
npx sonar-scanner
```

### Method 2: Using npm Scripts

```bash
# Generate mock coverage and run SonarQube analysis
npm run sonar:mock

# Start Docker and run complete analysis
npm run sonar:docker

# Generate mock coverage only
npm run generate-mock-coverage
```

### Method 3: Manual Execution

```bash
# Step 1: Start SonarQube
docker-compose -f docker-compose.sonarqube.yml up -d

# Step 2: Generate test coverage
npm run test:coverage

# Step 3: Run SonarQube scanner
npx sonar-scanner

# Step 4: Check results in SonarQube UI
# Open http://localhost:9000
```

### Method 4: Using Docker Directly

```bash
# Start SonarQube
docker-compose -f docker-compose.sonarqube.yml up -d

# Wait for SonarQube to be ready (check logs)
docker-compose -f docker-compose.sonarqube.yml logs -f

# Run test coverage
npm run test:coverage

# Run analysis
npx sonar-scanner
```

## 🎯 Quality Gates

### Default Quality Gate Configuration

The project is configured with the following quality gate conditions:

- **Coverage on New Code**: 80%
- **Duplicated Lines on New Code**: 0%
- **Maintainability Rating**: A
- **Reliability Rating**: A
- **Security Rating**: A
- **Security Hotspots Reviewed on New Code**: 100%

### Customizing Quality Gates

1. **Access SonarQube UI**: `http://localhost:9000`
2. **Navigate to**: Administration → Quality Gates
3. **Create New Quality Gate** or modify existing one
4. **Add Conditions**:
   - Coverage on New Code: 80%
   - Duplicated Lines on New Code: 0%
   - Maintainability Rating: A
   - Reliability Rating: A
   - Security Rating: A
   - Security Hotspots Reviewed on New Code: 100%

## 📊 Understanding Results

### Coverage Report

The test coverage report generates:
- **80% line coverage** (8 out of 10 lines covered)
- **100% function coverage** (1 out of 1 function covered)
- **Coverage file**: `coverage/lcov.info`

### Analysis Results

- **Minimal code analysis** is performed on App.jsx only
- **Quality gates pass** due to coverage data
- **Project structure preserved** without interference

## 🔧 Advanced Configuration

### Customizing Exclusions

To modify what gets excluded, edit `sonar-project.properties`:

```properties
# Exclude specific patterns
sonar.exclusions=**/*.test.js,**/*.spec.js,**/coverage/**/*

# Include specific files only
sonar.inclusions=src/**/*.js,src/**/*.jsx

# Exclude from coverage calculation
sonar.coverage.exclusions=**/*.test.js,**/*.spec.js
```

### Environment Variables

You can override configuration using environment variables:

```bash
# Set custom SonarQube URL
export SONAR_HOST_URL=http://your-sonarqube-server:9000

# Set custom token
export SONAR_TOKEN=your-sonarqube-token

# Run analysis with custom settings
npx sonar-scanner
```

### Docker Configuration

The `docker-compose.sonarqube.yml` file configures:

```yaml
version: '3'
services:
  sonarqube:
    image: sonarqube:community
    container_name: sonarqube
    ports:
      - "9000:9000"
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    restart: unless-stopped
    read_only: true
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp
      - /opt/sonarqube/temp
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. SonarQube Not Starting

```bash
# Check Docker logs
docker-compose -f docker-compose.sonarqube.yml logs

# Restart SonarQube
docker-compose -f docker-compose.sonarqube.yml down
docker-compose -f docker-compose.sonarqube.yml up -d
```

#### 2. Coverage Report Not Found

```bash
# Run test coverage
npm run test:coverage

# Verify coverage file exists
ls -la coverage/lcov.info
```

#### 3. Authentication Issues

```bash
# Reset SonarQube admin password
docker exec -it sonarqube bash
# Inside container:
sonar.sh restart
```

#### 4. Port Already in Use

```bash
# Check what's using port 9000
netstat -tulpn | grep :9000

# Change port in docker-compose.sonarqube.yml
ports:
  - "9001:9000"  # Use port 9001 instead
```

### Debug Mode

Enable debug logging:

```bash
# Run SonarQube scanner with debug
npx sonar-scanner -X

# Check detailed logs
docker-compose -f docker-compose.sonarqube.yml logs -f sonarqube
```

## 📈 Monitoring and Maintenance

### Regular Tasks

1. **Update SonarQube**: Keep the Docker image updated
2. **Backup Data**: Regular backups of SonarQube data
3. **Monitor Resources**: Check Docker resource usage
4. **Review Quality Gates**: Periodically review and update quality gates

### Performance Optimization

```bash
# Increase Docker memory limit
docker-compose -f docker-compose.sonarqube.yml up -d --scale sonarqube=1

# Monitor resource usage
docker stats sonarqube
```

## 📚 Additional Resources

- [SonarQube Documentation](https://docs.sonarqube.org/)
- [SonarQube Scanner Documentation](https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/)
- [Docker SonarQube Image](https://hub.docker.com/_/sonarqube)
- [Quality Gate Configuration](https://docs.sonarqube.org/latest/user-guide/quality-gates/)

## 🤝 Support

For additional support or questions:

1. Check the [Troubleshooting](#troubleshooting) section
2. Review SonarQube logs: `docker-compose -f docker-compose.sonarqube.yml logs`
3. Consult the [SonarQube Community](https://community.sonarsource.com/)
4. Contact the development team for project-specific issues 