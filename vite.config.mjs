// https://github.com/vitejs/vite/discussions/3448
import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import jsconfigPaths from 'vite-jsconfig-paths';

// ----------------------------------------------------------------------

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isBeta = mode === 'beta';
  const isProd = mode === 'production';

  return {
    plugins: [react(), jsconfigPaths()],
    // https://github.com/jpuri/react-draft-wysiwyg/issues/1317
    base: process.env.VITE_APP_BASE_NAME,
    define: {
      global: 'window'
    },
    resolve: {
      alias: [
        {
          find: /^~(.+)/,
          replacement: path.join(process.cwd(), 'node_modules/$1')
        },
        {
          find: /^src(.+)/,
          replacement: path.join(process.cwd(), 'src/$1')
        },
        // Exact matches for path aliases
        {
          find: 'config',
          replacement: path.resolve(process.cwd(), 'src/config.js')
        },
        {
          find: 'assets',
          replacement: path.resolve(process.cwd(), 'src/assets')
        },
        {
          find: 'components',
          replacement: path.resolve(process.cwd(), 'src/components')
        },
        {
          find: 'contexts',
          replacement: path.resolve(process.cwd(), 'src/contexts')
        },
        {
          find: 'routes',
          replacement: path.resolve(process.cwd(), 'src/routes')
        },
        {
          find: 'themes',
          replacement: path.resolve(process.cwd(), 'src/themes')
        },
        {
          find: 'pages',
          replacement: path.resolve(process.cwd(), 'src/pages')
        },
        {
          find: 'utils',
          replacement: path.resolve(process.cwd(), 'src/utils')
        },
        {
          find: 'hooks',
          replacement: path.resolve(process.cwd(), 'src/hooks')
        },
        {
          find: 'store',
          replacement: path.resolve(process.cwd(), 'src/store')
        },
        {
          find: 'menu-items',
          replacement: path.resolve(process.cwd(), 'src/menu-items')
        },
        {
          find: 'sections',
          replacement: path.resolve(process.cwd(), 'src/sections')
        },
        {
          find: 'layout',
          replacement: path.resolve(process.cwd(), 'src/layout')
        },
        {
          find: 'constants',
          replacement: path.resolve(process.cwd(), 'src/constants')
        },
        {
          find: 'data',
          replacement: path.resolve(process.cwd(), 'src/data')
        },
        {
          find: 'custom-components',
          replacement: path.resolve(process.cwd(), 'src/custom-components')
        },
        {
          find: 'profile-menu',
          replacement: path.resolve(process.cwd(), 'src/profile-menu')
        },
        {
          find: 'api',
          replacement: path.resolve(process.cwd(), 'src/api')
        }
      ]
    },
    server: {
      // this ensures that the browser opens upon server start
      open: true,
      // this sets a default port to 3000
      port: parseInt(env.VITE_APP_PORT || '3000'),
      proxy: {
        '/api': {
          target: env.VITE_APP_API_URL,
          changeOrigin: true,
          secure: false
        }
      }
    },
    preview: {
      // this ensures that the browser opens upon preview start
      open: true,
      // this sets a default port to 3000
      port: parseInt(env.VITE_APP_PORT || '3000')
    }
  };
});
