window.ENV_CONFIG = {
  // Backend Configuration
  VITE_APP_API_URL: 'https://api.ats.com',
  VITE_APP_ENV: 'production',
  VITE_APP_SERVICE_NAME: 'ats-frontend',
  VITE_APP_BACKEND_NAMESPACE: 'ats-backend',
  VITE_APP_API_VERSION: 'v1',
  
  // Application Configuration
  VITE_APP_BASE_NAME: '/',
  VITE_APP_PORT: 3000,
  
  // Feature Flags
  VITE_APP_ENABLE_ANALYTICS: true,
  VITE_APP_ENABLE_DEBUG: false,
  
  // Security Configuration
  VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/o/oauth2/auth',
  
  // Build Information
  BUILD_TIME: '2025-08-07T08:18:31.883Z',
  BUILD_ENV: 'production'
};

// Make configuration available globally
if (typeof window !== 'undefined') {
  window.ENV_CONFIG = window.ENV_CONFIG || {};
}

console.log('🚀 ATS Frontend Runtime Configuration Loaded:', window.ENV_CONFIG);
