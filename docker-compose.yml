version: '3.8'

services:
  # Production service
  ats-frontend-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_ENV: production
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Staging service
  ats-frontend-staging:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_ENV: development
    ports:
      - "3001:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - staging

  # Development service with hot reloading
  ats-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3002:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/dist
    env_file:
      - .env.local
    environment:
      - NODE_ENV=development
      - VITE_APP_PORT=3000
    restart: unless-stopped
    profiles:
      - dev 