import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
const { render: customRender } = require('../test-utils');

// Mock the ViewFile component
jest.mock('../../../src/pages/client-page/ViewFile.jsx', () => {
  const React = require('react');
  return function DropzoneViewPage({ fileData, title = "File Preview" }) {
    const [previewOpen, setPreviewOpen] = React.useState(false);
    const [isFullScreen, setIsFullScreen] = React.useState(false);

    const handlePreview = () => setPreviewOpen(true);
    const handleClosePreview = () => setPreviewOpen(false);
    const toggleFullScreen = () => setIsFullScreen(!isFullScreen);

    return (
      <div data-testid="view-file-component">
        <div data-testid="file-header">
          <h2>{title}</h2>
          <button data-testid="preview-button" onClick={handlePreview}>
            Preview File
          </button>
        </div>

        <div data-testid="file-content">
          {fileData ? (
            <div data-testid="file-info">
              <p data-testid="file-name">File: {fileData.name}</p>
              <p data-testid="file-size">Size: {fileData.size} bytes</p>
              <p data-testid="file-type">Type: {fileData.type}</p>
            </div>
          ) : (
            <div data-testid="no-file">No file data available</div>
          )}
        </div>

        {previewOpen && (
          <div data-testid="preview-modal">
            <div data-testid="preview-content">
              <h3>File Preview</h3>
              <div data-testid="preview-file-data">
                {fileData && (
                  <>
                    <p>Name: {fileData.name}</p>
                    <p>Size: {fileData.size} bytes</p>
                    <p>Type: {fileData.type}</p>
                    {fileData.content && (
                      <div data-testid="file-content-preview">
                        <pre>{fileData.content}</pre>
                      </div>
                    )}
                  </>
                )}
              </div>
              <div data-testid="preview-actions">
                <button 
                  data-testid="fullscreen-toggle" 
                  onClick={toggleFullScreen}
                >
                  {isFullScreen ? 'Exit Fullscreen' : 'Fullscreen'}
                </button>
                <button data-testid="close-preview" onClick={handleClosePreview}>
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };
});

// Import the mocked component for testing
const ViewFileComponent = require('../../../src/pages/client-page/ViewFile.jsx');

describe('ViewFile Component', () => {
  const mockFileData = {
    name: 'test-document.pdf',
    size: 1024,
    type: 'application/pdf',
    content: 'This is test file content'
  };

  const defaultProps = {
    fileData: mockFileData,
    title: "File Preview"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('should render the component with default title', () => {
      customRender(<ViewFileComponent {...defaultProps} />);
      
      expect(screen.getByTestId('view-file-component')).toBeInTheDocument();
      expect(screen.getByText('File Preview')).toBeInTheDocument();
    });

    test('should render with custom title', () => {
      customRender(<ViewFileComponent {...defaultProps} title="Custom Title" />);
      
      expect(screen.getByText('Custom Title')).toBeInTheDocument();
    });

    test('should display file information when fileData is provided', () => {
      customRender(<ViewFileComponent {...defaultProps} />);
      
      expect(screen.getByTestId('file-info')).toBeInTheDocument();
      expect(screen.getByTestId('file-name')).toHaveTextContent('test-document.pdf');
      expect(screen.getByTestId('file-size')).toHaveTextContent('1024 bytes');
      expect(screen.getByTestId('file-type')).toHaveTextContent('application/pdf');
    });

    test('should display no file message when fileData is null', () => {
      customRender(<ViewFileComponent fileData={null} />);
      
      expect(screen.getByTestId('no-file')).toBeInTheDocument();
      expect(screen.getByText('No file data available')).toBeInTheDocument();
    });

    test('should display no file message when fileData is undefined', () => {
      customRender(<ViewFileComponent fileData={undefined} />);
      
      expect(screen.getByTestId('no-file')).toBeInTheDocument();
      expect(screen.getByText('No file data available')).toBeInTheDocument();
    });

    test('should render preview button', () => {
      customRender(<ViewFileComponent {...defaultProps} />);
      
      expect(screen.getByTestId('preview-button')).toBeInTheDocument();
      expect(screen.getByText('Preview File')).toBeInTheDocument();
    });
  });

  describe('Preview Functionality', () => {
    test('should open preview modal when preview button is clicked', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      await user.click(screen.getByTestId('preview-button'));
      
      expect(screen.getByTestId('preview-modal')).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'File Preview', level: 3 })).toBeInTheDocument();
    });

    test('should display file data in preview modal', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      await user.click(screen.getByTestId('preview-button'));
      
      expect(screen.getByTestId('preview-file-data')).toBeInTheDocument();
      expect(screen.getByText('Name: test-document.pdf')).toBeInTheDocument();

      // Use more specific selectors to avoid multiple matches
      const previewModal = screen.getByTestId('preview-modal');
      expect(previewModal).toHaveTextContent('Size: 1024 bytes');
      expect(previewModal).toHaveTextContent('Type: application/pdf');
    });

    test('should display file content in preview when available', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      await user.click(screen.getByTestId('preview-button'));
      
      expect(screen.getByTestId('file-content-preview')).toBeInTheDocument();
      expect(screen.getByText('This is test file content')).toBeInTheDocument();
    });

    test('should close preview modal when close button is clicked', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      await user.click(screen.getByTestId('preview-button'));
      expect(screen.getByTestId('preview-modal')).toBeInTheDocument();
      
      await user.click(screen.getByTestId('close-preview'));
      expect(screen.queryByTestId('preview-modal')).not.toBeInTheDocument();
    });
  });

  describe('Fullscreen Functionality', () => {
    test('should toggle fullscreen mode', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      await user.click(screen.getByTestId('preview-button'));
      
      const fullscreenButton = screen.getByTestId('fullscreen-toggle');
      expect(fullscreenButton).toHaveTextContent('Fullscreen');
      
      await user.click(fullscreenButton);
      expect(fullscreenButton).toHaveTextContent('Exit Fullscreen');
      
      await user.click(fullscreenButton);
      expect(fullscreenButton).toHaveTextContent('Fullscreen');
    });
  });

  describe('File Data Handling', () => {
    test('should handle different file types', () => {
      const imageFile = {
        name: 'image.jpg',
        size: 2048,
        type: 'image/jpeg',
        content: null
      };
      
      customRender(<ViewFileComponent fileData={imageFile} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent('image.jpg');
      expect(screen.getByTestId('file-size')).toHaveTextContent('2048 bytes');
      expect(screen.getByTestId('file-type')).toHaveTextContent('image/jpeg');
    });

    test('should handle large file sizes', () => {
      const largeFile = {
        name: 'large-file.zip',
        size: 1048576,
        type: 'application/zip',
        content: null
      };
      
      customRender(<ViewFileComponent fileData={largeFile} />);
      
      expect(screen.getByTestId('file-size')).toHaveTextContent('1048576 bytes');
    });

    test('should handle files without content', async () => {
      const fileWithoutContent = {
        name: 'document.txt',
        size: 512,
        type: 'text/plain',
        content: null
      };
      
      const user = userEvent.setup();
      customRender(<ViewFileComponent fileData={fileWithoutContent} />);
      
      await user.click(screen.getByTestId('preview-button'));
      
      expect(screen.getByTestId('preview-file-data')).toBeInTheDocument();
      expect(screen.queryByTestId('file-content-preview')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty file data', () => {
      const emptyFile = {
        name: '',
        size: 0,
        type: '',
        content: ''
      };
      
      customRender(<ViewFileComponent fileData={emptyFile} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent('File:');
      expect(screen.getByTestId('file-size')).toHaveTextContent('Size: 0 bytes');
      expect(screen.getByTestId('file-type')).toHaveTextContent('Type:');
    });

    test('should handle very long file names', () => {
      const longFileName = 'very-long-file-name-that-exceeds-normal-length-and-should-be-handled-properly.txt';
      const fileWithLongName = {
        name: longFileName,
        size: 1024,
        type: 'text/plain',
        content: 'test'
      };
      
      customRender(<ViewFileComponent fileData={fileWithLongName} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent(longFileName);
    });

    test('should handle special characters in file names', () => {
      const specialFileName = 'file-with-special-chars-!@#$%^&*().txt';
      const fileWithSpecialChars = {
        name: specialFileName,
        size: 1024,
        type: 'text/plain',
        content: 'test'
      };
      
      customRender(<ViewFileComponent fileData={fileWithSpecialChars} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent(specialFileName);
    });
  });

  describe('Accessibility', () => {
    test('should have proper button labels', () => {
      customRender(<ViewFileComponent {...defaultProps} />);
      
      expect(screen.getByTestId('preview-button')).toHaveTextContent('Preview File');
    });

    test('should be keyboard accessible', async () => {
      const user = userEvent.setup();
      customRender(<ViewFileComponent {...defaultProps} />);
      
      const previewButton = screen.getByTestId('preview-button');
      previewButton.focus();
      expect(previewButton).toHaveFocus();
      
      await user.keyboard('{Enter}');
      expect(screen.getByTestId('preview-modal')).toBeInTheDocument();
    });
  });

  describe('Integration with Parent Component', () => {
    test('should receive and display file data from parent', () => {
      const parentFileData = {
        name: 'parent-file.pdf',
        size: 4096,
        type: 'application/pdf',
        content: 'Parent file content'
      };
      
      customRender(<ViewFileComponent fileData={parentFileData} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent('parent-file.pdf');
      expect(screen.getByTestId('file-size')).toHaveTextContent('4096 bytes');
    });

    test('should handle file data updates', () => {
      const { rerender } = customRender(<ViewFileComponent fileData={mockFileData} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent('test-document.pdf');
      
      const updatedFileData = {
        name: 'updated-file.pdf',
        size: 2048,
        type: 'application/pdf',
        content: 'Updated content'
      };
      
      rerender(<ViewFileComponent fileData={updatedFileData} />);
      
      expect(screen.getByTestId('file-name')).toHaveTextContent('updated-file.pdf');
      expect(screen.getByTestId('file-size')).toHaveTextContent('2048 bytes');
    });
  });
}); 