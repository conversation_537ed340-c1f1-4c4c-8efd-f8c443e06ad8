import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple test component
function TestComponent() {
  return <div data-testid="test-component">Test Component</div>;
}

describe('Basic Test', () => {
  test('should render test component', () => {
    render(<TestComponent />);
    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });
}); 