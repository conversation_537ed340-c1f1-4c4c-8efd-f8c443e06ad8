import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
const { render: customRender, mockClientData, mockClientList, mockAxios } = require('../test-utils.js');

// Mock the main index component
jest.mock('../../../src/pages/client-page/index.jsx', () => {
  const React = require('react');
  return function ClientPageIndex() {
    const [clients, setClients] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [selectedClient, setSelectedClient] = React.useState(null);
    const [isDialogOpen, setIsDialogOpen] = React.useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
    const [deleteClientId, setDeleteClientId] = React.useState(null);

    React.useEffect(() => {
      const fetchClients = async () => {
        try {
          const token = 'mock-token';
          const response = await mockAxios.get('http://localhost:8000/client/list', {
            headers: { Authorization: `Bearer ${token}` }
          });
          setClients(response.data.clients || mockClientList);
        } catch (error) {
          console.error('Error fetching clients:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchClients();
    }, []);

    const handleAddClick = () => {
      setIsDialogOpen(true);
    };

    const handleEditClick = (client) => {
      setSelectedClient(client);
      setIsDialogOpen(true);
    };

    const handleDeleteClick = (clientId) => {
      setDeleteClientId(clientId);
      setIsDeleteDialogOpen(true);
    };

    const handleConfirmDelete = async () => {
      try {
        await mockAxios.delete(`http://localhost:8000/clients/${deleteClientId}`);
        setClients(clients.filter(client => client.id !== deleteClientId));
        setIsDeleteDialogOpen(false);
        setDeleteClientId(null);
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    };

    const handleSearchChange = (event) => {
      setSearchTerm(event.target.value);
    };

    const filteredClients = clients.filter(client =>
      client.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email_id.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
      <div data-testid="client-page-index">
        <div data-testid="header-section">
          <h1>Client Management</h1>
          <div data-testid="search-section">
            <input
              data-testid="search-input"
              type="text"
              placeholder="Search clients..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <button data-testid="add-client-button" onClick={handleAddClick}>
            Add Client
          </button>
        </div>

        <div data-testid="table-container">
          {loading ? (
            <div data-testid="loading">Loading...</div>
          ) : (
            <table data-testid="client-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Contact</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredClients.map((client) => (
                  <tr key={client.id} data-testid={`client-row-${client.id}`}>
                    <td>{client.client_name}</td>
                    <td>{client.email_id}</td>
                    <td>{client.contacts_number}</td>
                    <td>
                      <button
                        data-testid={`edit-button-${client.id}`}
                        onClick={() => handleEditClick(client)}
                      >
                        Edit
                      </button>
                      <button
                        data-testid={`delete-button-${client.id}`}
                        onClick={() => handleDeleteClick(client.id)}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {isDialogOpen && (
          <div data-testid="client-dialog">
            <div data-testid="dialog-content">
              <h2>{selectedClient ? 'Edit Client' : 'Add New Client'}</h2>
              <button data-testid="close-dialog" onClick={() => setIsDialogOpen(false)}>
                Close
              </button>
            </div>
          </div>
        )}

        {isDeleteDialogOpen && (
          <div data-testid="delete-dialog">
            <div data-testid="delete-dialog-content">
              <h3>Confirm Delete</h3>
              <p>Are you sure you want to delete this client?</p>
              <button data-testid="confirm-delete" onClick={handleConfirmDelete}>
                Confirm
              </button>
              <button data-testid="cancel-delete" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };
});

// Import the mocked component for testing
const ClientPageIndex = require('../../../src/pages/client-page/index.jsx');

describe('Client Page Index Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    localStorage.setItem('serviceToken', 'mock-token');
  });

  describe('Initial Rendering', () => {
    test('should render the main client page', async () => {
      customRender(<ClientPageIndex />);
      
      expect(screen.getByTestId('client-page-index')).toBeInTheDocument();
      expect(screen.getByText('Client Management')).toBeInTheDocument();
    });

    test('should show loading state initially', () => {
      customRender(<ClientPageIndex />);
      
      expect(screen.getByTestId('loading')).toBeInTheDocument();
    });

    test('should render header elements', async () => {
      customRender(<ClientPageIndex />);
      
      await waitFor(() => {
        expect(screen.getByTestId('header-section')).toBeInTheDocument();
        expect(screen.getByTestId('search-section')).toBeInTheDocument();
        expect(screen.getByTestId('add-client-button')).toBeInTheDocument();
      });
    });
  });

  describe('Data Loading', () => {
    test('should fetch clients on component mount', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith(
          'http://localhost:8000/client/list',
          expect.objectContaining({
            headers: { Authorization: 'Bearer mock-token' }
          })
        );
      });
    });

    test('should display clients after loading', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
        expect(screen.getByText('Test Client')).toBeInTheDocument();
        expect(screen.getByText('Another Client')).toBeInTheDocument();
      });
    });

    test('should handle API errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    test('should filter clients by name', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByText('Test Client')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'Test');

      expect(screen.getByText('Test Client')).toBeInTheDocument();
      expect(screen.queryByText('Another Client')).not.toBeInTheDocument();
    });

    test('should filter clients by email', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, '<EMAIL>');

      expect(screen.getByText('Another Client')).toBeInTheDocument();
      expect(screen.queryByText('Test Client')).not.toBeInTheDocument();
    });

    test('should clear search results', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByText('Test Client')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'Test');
      await user.clear(searchInput);

      expect(screen.getByText('Test Client')).toBeInTheDocument();
      expect(screen.getByText('Another Client')).toBeInTheDocument();
    });
  });

  describe('Add Client Functionality', () => {
    test('should open add client dialog when add button is clicked', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('add-client-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('add-client-button'));

      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Add New Client')).toBeInTheDocument();
    });

    test('should close dialog when close button is clicked', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('add-client-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('add-client-button'));
      await user.click(screen.getByTestId('close-dialog'));

      expect(screen.queryByTestId('client-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Edit Client Functionality', () => {
    test('should open edit dialog when edit button is clicked', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('edit-button-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('edit-button-1'));

      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Client')).toBeInTheDocument();
    });
  });

  describe('Delete Client Functionality', () => {
    test('should open delete confirmation dialog when delete button is clicked', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('delete-button-1'));

      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });

    test('should delete client when confirmed', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });
      mockAxios.delete.mockResolvedValueOnce({});

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('delete-button-1'));
      await user.click(screen.getByTestId('confirm-delete'));

      await waitFor(() => {
        expect(mockAxios.delete).toHaveBeenCalledWith('http://localhost:8000/clients/1');
        expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
      });
    });

    test('should cancel delete when cancel button is clicked', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('delete-button-1'));
      await user.click(screen.getByTestId('cancel-delete'));

      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle delete API errors', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });
      mockAxios.delete.mockRejectedValueOnce(new Error('Delete failed'));

      const user = userEvent.setup();
      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('delete-button-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('delete-button-1'));
      await user.click(screen.getByTestId('confirm-delete'));

      await waitFor(() => {
        expect(mockAxios.delete).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    test('should have proper button labels', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      customRender(<ClientPageIndex />);

      await waitFor(() => {
        expect(screen.getByTestId('add-client-button')).toHaveTextContent('Add Client');
      });
    });

    test('should have proper table structure', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: { clients: mockClientList }
      });

      customRender(<ClientPageIndex />);

      await waitFor(() => {
        const table = screen.getByTestId('client-table');
        expect(table).toBeInTheDocument();
        expect(table.querySelector('thead')).toBeInTheDocument();
        expect(table.querySelector('tbody')).toBeInTheDocument();
      });
    });
  });
}); 