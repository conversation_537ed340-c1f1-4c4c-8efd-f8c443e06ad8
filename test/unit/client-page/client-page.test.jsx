import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render as customRender, mockAxios, mockClientData, mockClientList } from '../test-utils';

// Import all test suites
import './deleteBusinessInformation.test.jsx';
import './deleteClientContactInfo.test.jsx';
import './index.test.jsx';
import './ViewFile.test.jsx';
import './Dilognew.test.jsx';
import './add/add.test.jsx';
import './view/view.test.jsx';
import './edit/edit.test.jsx';

// Mock all components for integration testing

jest.mock('../../../src/pages/client-page/index.jsx', () => {
  const React = require('react');
  const { mockAxios, mockClientList } = require('../test-utils');

  return function ClientPageIndex() {
    const [clients, setClients] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [selectedClient, setSelectedClient] = React.useState(null);
    const [isDialogOpen, setIsDialogOpen] = React.useState(false);

    React.useEffect(() => {
      const fetchClients = async () => {
        try {
          const response = await mockAxios.get('/clients');
          setClients(response.data || mockClientList);
        } catch (error) {
          console.error('Error fetching clients:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchClients();
    }, []);

    const handleAddClick = () => {
      setIsDialogOpen(true);
    };

    const handleEditClick = (client) => {
      setSelectedClient(client);
      setIsDialogOpen(true);
    };

    return React.createElement('div', { 'data-testid': 'client-page-index' },
      React.createElement('div', { 'data-testid': 'header-section' },
        React.createElement('h1', null, 'Client Management'),
        React.createElement('button', {
          'data-testid': 'add-client-button',
          onClick: handleAddClick
        }, 'Add Client')
      ),
      React.createElement('div', { 'data-testid': 'table-container' },
        loading ?
          React.createElement('div', { 'data-testid': 'loading' }, 'Loading...') :
          React.createElement('table', { 'data-testid': 'client-table' },
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'Name'),
                React.createElement('th', null, 'Email'),
                React.createElement('th', null, 'Contact'),
                React.createElement('th', null, 'Actions')
              )
            ),
            React.createElement('tbody', null,
              clients.map((client) =>
                React.createElement('tr', {
                  key: client.id,
                  'data-testid': `client-row-${client.id}`
                },
                  React.createElement('td', null, client.client_name),
                  React.createElement('td', null, client.email_id),
                  React.createElement('td', null, client.contacts_number),
                  React.createElement('td', null,
                    React.createElement('button', {
                      'data-testid': `edit-button-${client.id}`,
                      onClick: () => handleEditClick(client)
                    }, 'Edit')
                  )
                )
              )
            )
          )
      ),
      isDialogOpen && React.createElement('div', { 'data-testid': 'client-dialog' },
        React.createElement('div', { 'data-testid': 'dialog-content' },
          React.createElement('h2', null, selectedClient ? 'Edit Client' : 'Add New Client'),
          React.createElement('button', {
            'data-testid': 'close-dialog',
            onClick: () => setIsDialogOpen(false)
          }, 'Close')
        )
      )
    );
  };
});

// Import the mocked component for testing
const ClientPageIndexComponent = require('../../../src/pages/client-page/index.jsx');

describe('Client Page Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    localStorage.setItem('serviceToken', 'mock-token');
  });

  describe('Component Integration', () => {
    test('should integrate all components together', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-page-index')).toBeInTheDocument();
        expect(screen.getByText('Client Management')).toBeInTheDocument();
      });
    });

    test('should handle client data flow between components', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Test client row interaction
      const editButton = screen.getByTestId('edit-button-1');
      await user.click(editButton);

      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Client')).toBeInTheDocument();
    });
  });

  describe('Data Flow Testing', () => {
    test('should handle data fetching and state management', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByText('Test Client')).toBeInTheDocument();
        expect(screen.getByText('Another Client')).toBeInTheDocument();
      });
    });

    test('should handle API errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });
    });
  });

  describe('User Interaction Flow', () => {
    test('should handle complete user workflow', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('add-client-button')).toBeInTheDocument();
      });

      // Test add client flow
      await user.click(screen.getByTestId('add-client-button'));
      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Add New Client')).toBeInTheDocument();

      // Test close dialog
      await user.click(screen.getByTestId('close-dialog'));
      expect(screen.queryByTestId('client-dialog')).not.toBeInTheDocument();

      // Test edit client flow
      const editButton = screen.getByTestId('edit-button-1');
      await user.click(editButton);
      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Client')).toBeInTheDocument();
    });
  });

  describe('Performance Testing', () => {
    test('should handle large datasets efficiently', async () => {
      const largeClientList = Array.from({ length: 100 }, (_, index) => ({
        id: index + 1,
        client_name: `Client ${index + 1}`,
        email_id: `client${index + 1}@test.com`,
        contacts_number: `123456789${index.toString().padStart(2, '0')}`,
        website: `https://client${index + 1}.com`,
        industry: 'Technology',
        country: 'USA',
        saret: 'California',
        city: 'San Francisco',
        business_unit: 'IT',
        category: 'Enterprise',
        postal: '94105',
        created_by: 'admin',
        created_on: '2024-01-01'
      }));

      mockAxios.get.mockResolvedValueOnce({
        data: largeClientList
      });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Verify first and last clients are rendered
      expect(screen.getByText('Test Client')).toBeInTheDocument();
      expect(screen.getByText('Another Client')).toBeInTheDocument();
    });
  });

  describe('Accessibility Integration', () => {
    test('should maintain accessibility across all components', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Test keyboard navigation
      const addButton = screen.getByTestId('add-client-button');
      addButton.focus();
      expect(addButton).toHaveFocus();

      // Test table structure
      const table = screen.getByTestId('client-table');
      expect(table.querySelector('thead')).toBeInTheDocument();
      expect(table.querySelector('tbody')).toBeInTheDocument();
    });
  });

  describe('Error Boundary Testing', () => {
    test('should handle component errors gracefully', async () => {
      // Mock a component that throws an error
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      // This would normally be caught by an error boundary
      expect(() => {
        render(<ErrorComponent />);
      }).toThrow('Test error');
    });
  });

  describe('State Management Testing', () => {
    test('should handle complex state transitions', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Test multiple state changes
      await user.click(screen.getByTestId('add-client-button'));
      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();

      await user.click(screen.getByTestId('close-dialog'));
      expect(screen.queryByTestId('client-dialog')).not.toBeInTheDocument();

      await user.click(screen.getByTestId('add-client-button'));
      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
    });
  });

  describe('API Integration Testing', () => {
    test('should handle all API endpoints correctly', async () => {
      // Mock all API calls
      mockAxios.get.mockResolvedValue({ data: mockClientList });
      mockAxios.post.mockResolvedValue({ data: { id: 1, success: true } });
      mockAxios.put.mockResolvedValue({ data: { id: 1, success: true } });
      mockAxios.delete.mockResolvedValue({ data: { success: true } });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('http://localhost:8000/client/list', expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        }));
      });
    });

    test('should handle API authentication correctly', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('http://localhost:8000/client/list', expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token'
          })
        }));
      });

      // Verify token is stored
      expect(localStorage.getItem('serviceToken')).toBe('mock-token');
    });
  });

  describe('Cross-Component Communication', () => {
    test('should handle data flow between parent and child components', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: mockClientList
      });

      const user = userEvent.setup();
      customRender(<ClientPageIndexComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Test that child components receive correct props
      const editButton = screen.getByTestId('edit-button-1');
      await user.click(editButton);

      expect(screen.getByTestId('client-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Client')).toBeInTheDocument();
    });
  });

  describe('Memory Leak Testing', () => {
    test('should not cause memory leaks with frequent updates', async () => {
      mockAxios.get.mockResolvedValue({
        data: mockClientList
      });

      const { rerender, unmount } = customRender(<ClientPageIndexComponent />);

      // Simulate multiple re-renders
      for (let i = 0; i < 10; i++) {
        rerender(<ClientPageIndexComponent />);
      }

      // Verify component still works
      await waitFor(() => {
        expect(screen.getByTestId('client-table')).toBeInTheDocument();
      });

      // Clean up
      unmount();
    });
  });
});

// Export for use in other test files
export {
  ClientPageIndexComponent,
  mockAxios,
  mockClientData,
  mockClientList,
  customRender
}; 