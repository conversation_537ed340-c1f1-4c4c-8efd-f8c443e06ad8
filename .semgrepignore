# Semgrep ignore file
# Add patterns to ignore specific findings

# Ignore test files
test/
**/*.test.js
**/*.test.jsx
**/*.spec.js
**/*.spec.jsx

# Ignore build and node_modules
node_modules/
build/
dist/
coverage/

# Ignore specific file patterns
**/*.min.js
**/*.bundle.js

# Ignore specific directories
public/
assets/
docs/
.idea/

# Add specific rule IDs to ignore
# Example: # nosemgrep: javascript.security.audit.regex.dos

# Ignore specific findings from the scan
# Generic secrets and API keys
.env
index.html

# Ignore unsafe format string findings in console.log statements
# These are low-risk logging statements
**/EditApplicantsPermissions.jsx
**/EditCampusesPermissions.jsx
**/EditClientPermissions.jsx
**/EditEboardingPermission.jsx
**/EditEmploymentTestPermissions.jsx
**/EditEvaluationTemplatePermissions.jsx
**/EditJobPostingPermissions.jsx
**/EditJobRequisitionPermissions.jsx
**/EditJobTemplatePermissions.jsx
**/EditLeadsPermissions.jsx
**/EditPlacementPermission.jsx
**/EditTalentBenchPermissions.jsx
**/EditVendorsPermissions.jsx
**/EditBIReportPermissions.jsx
**/ClientApprovalsTab.jsx
**/ContactApprovalTab.jsx
**/addRoles.jsx
**/editRoles.jsx
**/AddUsers.jsx
**/EditUsers.jsx

# Ignore specific rule patterns
javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring
generic.secrets.security.detected-generic-api-key.detected-generic-api-key
generic.secrets.security.detected-jwt-token.detected-jwt-token
generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key
html.security.audit.missing-integrity.missing-integrity 