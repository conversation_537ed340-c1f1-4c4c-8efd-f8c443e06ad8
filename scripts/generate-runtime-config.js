#!/usr/bin/env node

/**
 * ATS Frontend Application - Runtime Configuration Generator
 * 
 * This script generates an env-config.js file that can be mounted as a ConfigMap
 * in Kubernetes to enable dynamic backend switching without rebuilding the Docker image.
 * 
 * Usage:
 *   node scripts/generate-runtime-config.js [environment] [backend-url]
 * 
 * Examples:
 *   node scripts/generate-runtime-config.js production https://api.ats.com
 *   node scripts/generate-runtime-config.js staging https://staging-api.ats.com
 *   node scripts/generate-runtime-config.js development http://localhost:3010
 */

const fs = require('fs');
const path = require('path');

// Default configurations
const DEFAULT_CONFIGS = {
  development: {
    backendUrl: 'http://localhost:3010',
    environment: 'development',
    serviceName: 'ats-frontend',
    namespace: 'ats-backend',
    apiVersion: 'v1',
    baseName: '/',
    port: 3000,
    enableAnalytics: false,
    enableDebug: true
  },
  staging: {
    backendUrl: 'https://staging-api.ats.com',
    environment: 'staging',
    serviceName: 'ats-frontend',
    namespace: 'ats-backend-staging',
    apiVersion: 'v1',
    baseName: '/',
    port: 3000,
    enableAnalytics: true,
    enableDebug: false
  },
  production: {
    backendUrl: 'https://api.ats.com',
    environment: 'production',
    serviceName: 'ats-frontend',
    namespace: 'ats-backend',
    apiVersion: 'v1',
    baseName: '/',
    port: 3000,
    enableAnalytics: true,
    enableDebug: false
  }
};

function generateRuntimeConfig(environment = 'production', customBackendUrl = null) {
  console.log(`🔧 Generating runtime configuration for ${environment} environment...`);
  
  const config = { ...DEFAULT_CONFIGS[environment] };
  
  if (customBackendUrl) {
    config.backendUrl = customBackendUrl;
    console.log(`📡 Using custom backend URL: ${customBackendUrl}`);
  }
  
  // Generate the env-config.js content
  const envConfigContent = `window.ENV_CONFIG = {
  // Backend Configuration
  VITE_APP_API_URL: '${config.backendUrl}',
  VITE_APP_ENV: '${config.environment}',
  VITE_APP_SERVICE_NAME: '${config.serviceName}',
  VITE_APP_BACKEND_NAMESPACE: '${config.namespace}',
  VITE_APP_API_VERSION: '${config.apiVersion}',
  
  // Application Configuration
  VITE_APP_BASE_NAME: '${config.baseName}',
  VITE_APP_PORT: ${config.port},
  
  // Feature Flags
  VITE_APP_ENABLE_ANALYTICS: ${config.enableAnalytics},
  VITE_APP_ENABLE_DEBUG: ${config.enableDebug},
  
  // Security Configuration
  VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/o/oauth2/auth',
  
  // Build Information
  BUILD_TIME: '${new Date().toISOString()}',
  BUILD_ENV: '${environment}'
};

// Make configuration available globally
if (typeof window !== 'undefined') {
  window.ENV_CONFIG = window.ENV_CONFIG || {};
}

console.log('🚀 ATS Frontend Runtime Configuration Loaded:', window.ENV_CONFIG);
`;

  // Write the configuration to public/env-config.js
  const outputPath = path.join(process.cwd(), 'public', 'env-config.js');
  
  // Ensure the public directory exists
  const publicDir = path.dirname(outputPath);
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  fs.writeFileSync(outputPath, envConfigContent);
  
  console.log(`✅ Runtime configuration generated: ${outputPath}`);
  console.log(`📊 Configuration Summary:`);
  console.log(`   Environment: ${config.environment}`);
  console.log(`   Backend URL: ${config.backendUrl}`);
  console.log(`   Service Name: ${config.serviceName}`);
  console.log(`   Namespace: ${config.namespace}`);
  console.log(`   Analytics: ${config.enableAnalytics}`);
  console.log(`   Debug Mode: ${config.enableDebug}`);
  
  return config;
}

// CLI support
if (require.main === module) {
  const args = process.argv.slice(2);
  const environment = args[0] || 'production';
  const customBackendUrl = args[1] || null;
  
  if (!DEFAULT_CONFIGS[environment]) {
    console.error(`❌ Invalid environment: ${environment}`);
    console.error(`   Valid environments: ${Object.keys(DEFAULT_CONFIGS).join(', ')}`);
    process.exit(1);
  }
  
  try {
    generateRuntimeConfig(environment, customBackendUrl);
    console.log(`\n🎉 Runtime configuration generated successfully!`);
    console.log(`📝 To use this in Kubernetes, create a ConfigMap:`);
    console.log(`   kubectl create configmap ats-frontend-config --from-file=public/env-config.js -n your-namespace`);
  } catch (error) {
    console.error(`❌ Error generating runtime configuration:`, error.message);
    process.exit(1);
  }
}

module.exports = { generateRuntimeConfig, DEFAULT_CONFIGS }; 