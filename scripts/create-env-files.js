#!/usr/bin/env node

/**
 * ATS Frontend Application - Environment Files Generator
 * 
 * This script creates the required .env files for CI/CD compatibility.
 * It generates environment files for production, development, and beta environments.
 */

const fs = require('fs');
const path = require('path');

// Environment configurations
const ENV_CONFIGS = {
  production: {
    VITE_APP_API_URL: 'https://api.ats.com',
    VITE_APP_BASE_NAME: '/',
    VITE_APP_PORT: '3000',
    VITE_APP_ENV: 'production',
    VITE_APP_ENABLE_ANALYTICS: 'true',
    VITE_APP_ENABLE_DEBUG: 'false',
    VITE_APP_SERVICE_NAME: 'ats-frontend',
    VITE_APP_BACKEND_NAMESPACE: 'ats-backend',
    VITE_APP_API_VERSION: 'v1',
    VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/o/oauth2/auth'
  },
  development: {
    VITE_APP_API_URL: 'http://localhost:3010',
    VITE_APP_BASE_NAME: '/',
    VITE_APP_PORT: '3000',
    VITE_APP_ENV: 'development',
    VITE_APP_ENABLE_ANALYTICS: 'false',
    VITE_APP_ENABLE_DEBUG: 'true',
    VITE_APP_SERVICE_NAME: 'ats-frontend',
    VITE_APP_BACKEND_NAMESPACE: 'ats-backend-dev',
    VITE_APP_API_VERSION: 'v1',
    VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/o/oauth2/auth'
  },
  beta: {
    VITE_APP_API_URL: 'https://beta-api.ats.com',
    VITE_APP_BASE_NAME: '/',
    VITE_APP_PORT: '3000',
    VITE_APP_ENV: 'beta',
    VITE_APP_ENABLE_ANALYTICS: 'true',
    VITE_APP_ENABLE_DEBUG: 'false',
    VITE_APP_SERVICE_NAME: 'ats-frontend',
    VITE_APP_BACKEND_NAMESPACE: 'ats-backend-beta',
    VITE_APP_API_VERSION: 'v1',
    VITE_APP_GOOGLE_OAUTH_URL: 'https://accounts.google.com/o/oauth2/auth'
  }
};

function createEnvFile(environment, config) {
  const fileName = `.env.${environment}`;
  const filePath = path.join(process.cwd(), fileName);
  
  let content = `# ${environment.toUpperCase()} Environment Configuration\n`;
  content += `# Generated automatically for CI/CD compatibility\n\n`;
  
  Object.entries(config).forEach(([key, value]) => {
    content += `${key}=${value}\n`;
  });
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ Created ${fileName}`);
  
  return filePath;
}

function main() {
  console.log('🔧 Creating environment files for CI/CD compatibility...\n');
  
  try {
    // Create environment files for each environment
    Object.entries(ENV_CONFIGS).forEach(([env, config]) => {
      createEnvFile(env, config);
    });
    
    console.log('\n🎉 All environment files created successfully!');
    console.log('\n📁 Created files:');
    console.log('   - .env.production');
    console.log('   - .env.development');
    console.log('   - .env.beta');
    
    console.log('\n🚀 CI/CD Compatibility:');
    console.log('   - Docker builds will now work with ENV_FILE argument');
    console.log('   - BUILD_SCRIPT argument will use correct environment');
    console.log('   - Runtime configuration will be generated properly');
    
  } catch (error) {
    console.error('❌ Error creating environment files:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createEnvFile, ENV_CONFIGS }; 