# ATS Frontend Application - Docker Setup Guide

This guide explains how to build and run the ATS Frontend Application using Docker.

## 🐳 Docker Configuration

The project includes multiple Docker configurations for different environments:

### Files Overview
- `Dockerfile` - Production build with nginx
- `Dockerfile.dev` - Development build with hot reloading
- `docker-compose.yml` - Multi-service orchestration
- `nginx.conf` - Nginx configuration for production
- `scripts/generate-runtime-config.js` - Runtime configuration generator

## 🚀 Quick Start

### Production Build
```bash
# Build production image
docker build -t ats-frontend:latest .

# Run production container
docker run -p 3000:80 ats-frontend:latest
```

### Development Build
```bash
# Build development image
docker build -f Dockerfile.dev -t ats-frontend:dev .

# Run development container
docker run -p 3002:3000 -v $(pwd):/app ats-frontend:dev
```

## 🐙 Docker Compose

### Production Environment
```bash
# Start production service
docker-compose up ats-frontend-app

# Or build and start
docker-compose up --build ats-frontend-app
```

### Staging Environment
```bash
# Start staging service
docker-compose --profile staging up ats-frontend-staging
```

### Development Environment
```bash
# Start development service with hot reloading
docker-compose --profile dev up ats-frontend-dev
```

## 🔧 Build Arguments

### Environment-Specific Builds
```bash
# Production build
docker build --build-arg BUILD_ENV=production -t ats-frontend:prod .

# Development build
docker build --build-arg BUILD_ENV=development -t ats-frontend:staging .

# Beta build
docker build --build-arg BUILD_ENV=beta -t ats-frontend:beta .
```

## 🌍 Runtime Configuration

The application supports dynamic runtime configuration through the `env-config.js` file:

### Generate Runtime Config
```bash
# Generate production config
node scripts/generate-runtime-config.js production

# Generate staging config
node scripts/generate-runtime-config.js staging

# Generate with custom backend URL
node scripts/generate-runtime-config.js production https://api.ats.com
```

### Kubernetes ConfigMap
```bash
# Create ConfigMap from generated config
kubectl create configmap ats-frontend-config \
  --from-file=public/env-config.js \
  -n your-namespace
```

## 📊 Health Checks

The application includes health check endpoints:
- **Production/Staging**: `http://localhost/health`
- **Development**: `http://localhost:3000/health`

## 🔒 Security Features

- Non-root nginx user
- Security headers in nginx configuration
- Content Security Policy (CSP)
- XSS protection
- Frame options

## 📁 File Structure

```
ATS-Frontend-Application/
├── Dockerfile              # Production build
├── Dockerfile.dev          # Development build
├── docker-compose.yml      # Multi-service orchestration
├── nginx.conf             # Nginx configuration
├── scripts/
│   └── generate-runtime-config.js  # Runtime config generator
└── public/
    └── env-config.js      # Runtime configuration (generated)
```

## 🛠️ Development Workflow

### Local Development
```bash
# Start development container
docker-compose --profile dev up ats-frontend-dev

# Access application
open http://localhost:3002
```

### Hot Reloading
The development container includes:
- Volume mounting for source code
- Hot reloading with Vite
- Live code changes without rebuild

### Environment Variables
Create `.env.local` for development:
```env
VITE_APP_API_URL=http://localhost:3010
VITE_APP_BASE_NAME=/
VITE_APP_PORT=3000
```

## 🚀 Production Deployment

### Build Production Image
```bash
# Build with production environment
docker build --build-arg BUILD_ENV=production -t ats-frontend:latest .
```

### Run Production Container
```bash
# Run with port mapping
docker run -p 3000:80 ats-frontend:latest

# Run with environment variables
docker run -p 3000:80 \
  -e NODE_ENV=production \
  ats-frontend:latest
```

### Docker Compose Production
```bash
# Start production stack
docker-compose up ats-frontend-app

# Start with rebuild
docker-compose up --build ats-frontend-app
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   lsof -i :3000
   
   # Use different port
   docker run -p 3001:80 ats-frontend:latest
   ```

2. **Build Failures**
   ```bash
   # Clean build
   docker build --no-cache -t ats-frontend:latest .
   
   # Check build logs
   docker-compose build --progress=plain
   ```

3. **Runtime Configuration Issues**
   ```bash
   # Regenerate runtime config
   node scripts/generate-runtime-config.js production
   
   # Rebuild with new config
   docker build --build-arg BUILD_ENV=production -t ats-frontend:latest .
   ```

### Debug Commands
```bash
# Check container logs
docker logs <container-id>

# Enter running container
docker exec -it <container-id> sh

# Check nginx configuration
docker exec <container-id> nginx -t
```

## 📈 Performance Optimization

### Multi-stage Build
- **deps stage**: Install dependencies
- **builder stage**: Build application
- **runner stage**: Serve with nginx

### Image Size Optimization
- Alpine Linux base image
- Multi-stage build
- .dockerignore exclusions
- Non-root user

### Caching Strategy
- Layer caching for dependencies
- Build argument for environment
- Volume mounting for development

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Build and Deploy
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: |
          docker build --build-arg BUILD_ENV=production -t ats-frontend:${{ github.sha }} .
      - name: Push to registry
        run: |
          docker push your-registry/ats-frontend:${{ github.sha }}
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [Vite Build Tool](https://vitejs.dev/)
- [React Development](https://reactjs.org/)

---

**Note**: This Docker setup is optimized for the ATS Frontend Application and follows best practices for security, performance, and maintainability. 