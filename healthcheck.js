#!/usr/bin/env node

/**
 * ATS Frontend Application - Health Check Script
 * 
 * This script provides a simple health check endpoint for the development environment.
 * It can be used to verify that the application is running correctly.
 */

const http = require('http');

const server = http.createServer((req, res) => {
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ats-frontend',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime()
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

const port = process.env.PORT || 3000;

server.listen(port, () => {
  console.log(`🏥 Health check server running on port ${port}`);
  console.log(`📊 Health endpoint: http://localhost:${port}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  server.close(() => {
    console.log('✅ Health check server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  server.close(() => {
    console.log('✅ Health check server closed');
    process.exit(0);
  });
}); 