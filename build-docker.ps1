# ATS Frontend Application - Docker Build Script (PowerShell)
# 
# This script simplifies building Docker images for different environments.
# 
# Usage:
#   .\build-docker.ps1 [environment] [tag]
# 
# Examples:
#   .\build-docker.ps1 production latest
#   .\build-docker.ps1 development staging
#   .\build-docker.ps1 beta v1.0.0

param(
    [Parameter(Position=0)]
    [string]$Environment = "production",
    
    [Parameter(Position=1)]
    [string]$Tag = "latest"
)

# Default values
$ImageName = "ats-frontend"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Validate environment
$ValidEnvironments = @("production", "development", "beta")
if ($Environment -notin $ValidEnvironments) {
    Write-Error "Invalid environment: $Environment"
    Write-Error "Valid environments: $($ValidEnvironments -join ', ')"
    exit 1
}

Write-Status "Building ATS Frontend Application Docker image..."
Write-Status "Environment: $Environment"
Write-Status "Tag: $Tag"
Write-Status "Image: $ImageName`:$Tag"

# Generate runtime configuration
Write-Status "Generating runtime configuration for $Environment..."
try {
    node scripts/generate-runtime-config.js $Environment
    Write-Success "Runtime configuration generated successfully"
}
catch {
    Write-Error "Failed to generate runtime configuration"
    exit 1
}

# Build Docker image
Write-Status "Building Docker image..."
try {
    docker build `
        --build-arg BUILD_ENV=$Environment `
        --tag "$ImageName`:$Tag" `
        --tag "$ImageName`:$Environment" `
        .

    Write-Success "Docker image built successfully!"
    Write-Status "Image: $ImageName`:$Tag"
    Write-Status "Image: $ImageName`:$Environment"
    
    # Show image info
    Write-Status "Image details:"
    docker images "$ImageName`:$Tag" --format "table {{.Repository}}`t{{.Tag}}`t{{.Size}}`t{{.CreatedAt}}"
    
    Write-Status "To run the container:"
    Write-Host "  docker run -p 3000:80 $ImageName`:$Tag" -ForegroundColor Cyan
    Write-Host "  docker run -p 3000:80 $ImageName`:$Environment" -ForegroundColor Cyan
    
}
catch {
    Write-Error "Failed to build Docker image"
    exit 1
}

Write-Success "Build completed successfully!" 