name: Docker Build and Push to DOCR (Universal Runtime Config)

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: [self-hosted, Linux]

    env:
      APP_NAME: ats-frontend-application

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Set Universal Build Configuration
        run: |
          BRANCH_NAME="${{ github.ref_name }}"

          # Universal runtime configuration approach
          # Backend URLs will be configured at runtime via ConfigMap
          echo "🚀 Using universal runtime configuration approach"
          echo "📦 Branch: $BRANCH_NAME"
          echo "🔧 Build: Universal image with runtime backend switching"

          # Use production build for ATS application
          BUILD_SCRIPT="build:prod"
          ENV_FILE=".env.production"  # Default env file for build-time variables only

          echo "ENV_FILE=$ENV_FILE" >> $GITHUB_ENV
          echo "BUILD_SCRIPT=$BUILD_SCRIPT" >> $GITHUB_ENV
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      - name: Authenticate Docker with DOCR
        run: doctl registry login

      - name: Build Universal Docker Image
        run: |
          echo "🔨 Building universal Docker image with runtime configuration"
          echo "📁 ENV_FILE: ${{ env.ENV_FILE }} (build-time variables only)"
          echo "🚀 BUILD_SCRIPT: ${{ env.BUILD_SCRIPT }} (universal runtime build)"
          echo "🌿 BRANCH: ${{ env.BRANCH_NAME }}"

          docker build \
            --build-arg ENV_FILE=${{ env.ENV_FILE }} \
            --build-arg BUILD_SCRIPT=${{ env.BUILD_SCRIPT }} \
            --label "branch=${{ env.BRANCH_NAME }}" \
            --label "runtime-config=enabled" \
            --label "backend-agnostic=true" \
            -t ${{ env.APP_NAME }} .

      - name: Tag Universal Docker Image
        run: |
          IMAGE_TAG="registry.digitalocean.com/doks-registry/${{ env.APP_NAME }}:latest"
          docker tag ${{ env.APP_NAME }} $IMAGE_TAG
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

          echo "🏷️  Tagged universal image: $IMAGE_TAG"
          echo "🔧 Image supports runtime backend switching"
          echo "📦 Compatible with: Spring, Django, Nest backends"

      - name: Push Universal Docker Image
        run: |
          echo "📤 Pushing universal Docker image to registry..."
          docker push ${{ env.IMAGE_TAG }}

          echo "✅ Successfully pushed universal image!"
          echo "🌐 Image: ${{ env.IMAGE_TAG }}"
          echo "🔄 Backend switching: Enabled via ConfigMap"
          echo "🚀 Ready for deployment to any environment" 