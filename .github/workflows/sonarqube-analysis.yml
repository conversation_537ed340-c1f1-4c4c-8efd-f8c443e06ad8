name: SonarQube Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true

jobs:
  sonarqube:
    runs-on: [self-hosted, Linux, X64]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install dependencies
        run: npm install
      - name: Run Tests (with coverage)
        run: npm run test:coverage
      - name: Install SonarScanner
        run: npm install -g sonarqube-scanner
      - name: Run SonarQube Analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          sonar-scanner \
            -Dsonar.projectKey=ats-frontend-application \
            -Dsonar.projectName="ATS Frontend Application" \
            -Dsonar.sources=src \
            -Dsonar.tests=test \
            -Dsonar.host.url=http://*************:9000/ \
            -Dsonar.login=$SONAR_TOKEN \
            -Dsonar.coverage.exclusions=src/components/ui/**,src/config.js,src/index.jsx,src/setupTests.js \
            -Dsonar.test.inclusions=test/unit/**/*.test.js,test/unit/**/*.test.jsx,test/integration/**/*.test.js,test/integration/**/*.test.jsx \
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info \
            -Dsonar.sourceEncoding=UTF-8
      - name: Wait for SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: http://*************:9000/ 