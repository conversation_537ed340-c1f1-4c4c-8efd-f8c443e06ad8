# Documentation (generated)
docs/

# Node modules
node_modules/

# Build output
dist/
build/
coverage/

# Test files
test/
*.test.js
*.test.jsx
*.spec.js
*.spec.jsx

# Development files
.git/
.gitignore
README.md
*.md

# IDE files
.vscode/
.idea/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
!.env*

# SonarQube
.sonar/
.sonarqube/
.scannerwork/

# Semgrep
.semgrepignore
semgrep-results.*

# Docker files
Dockerfile
.dockerignore
docker-compose*.yml

# Scripts (keep generate-runtime-config.js for Docker builds)
run-*.sh

# Config files
jest.config.js
.eslintrc
.prettierrc
tsconfig*.json
jsconfig.json
vite.config.mjs 