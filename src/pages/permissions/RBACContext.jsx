// RBACProvider.js
import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import JWTContext from 'contexts/JWTContext';
import { PERMISSIONS } from 'constants';

const defaultProvider = {
  rbacRoles: null,
};

const RBACContext = createContext(defaultProvider);

export const useRBAC = () => {
  return useContext(RBACContext);
};

// Helper function to check permissions
const hasRequiredPermissions = (rolePermissions, requiredPermission) => 
  rolePermissions === PERMISSIONS.FULL_ACCESS || (rolePermissions & requiredPermission) === requiredPermission;

export const RBACProvider = ({ children }) => {
  const { user } = useContext(JWTContext)


   if (!user) {
    console.warn("⚠️ User is null in RBACProvider. Waiting for authentication...");
    return <RBACContext.Provider value={defaultProvider}>{children}</RBACContext.Provider>;
  }




  console.log("this is for users check in RBAC", user)
  const userRoles = user.role?.permissions || " "; // Default to empty array if roles is undefined
  console.log("this is to test user roles", userRoles)
  // 🔹 Ensure `permissions` exists before accessing it
  // const initialPermissions = userRoles.length > 0 ? userRoles[0]?.permissions || [] : [];

  const [rbacRoles, setRbacRoles] = useState(userRoles);
  console.log("this is an rbacRoles**********", rbacRoles)
  // const [rbacRoles, setRbacRoles] = useState( user.roles[0]?.permissions || []);

  // Update rbacRoles whenever user.permissionsDTOList changes
  useEffect(() => {
    if (user.role?.permissions) {
      setRbacRoles(user.role?.permissions);
    }
  }, [user.role?.permissions]);

  const canAccessTopMenu = useCallback((menuName, requiredPermission) => {
    console.log(`🔍 Checking Top Menu RBAC for ${menuName}`);

    // ✅ Find "Top_Menu" in RBAC JSON
    const topMenu = rbacRoles.find((role) => role.name === menuName && role.type === "MENU");
    if (!topMenu) {
      console.warn(`❌ Menu ${menuName} not found!`);
      return false;
    }

    // ✅ Check permissions only at "MENU" level
    const hasPermission = (topMenu.permissions & requiredPermission) === requiredPermission;
    console.log(`✅ Permission Check for ${menuName}: ${hasPermission ? "Allowed" : "Denied"}`);

    return hasPermission;
  }, [rbacRoles]);


  // Unified permission-checking function for both page and field levels
  const canAccess = useCallback((menuName, pageName, requiredPermission, fieldName = null) => {
    if (requiredPermission === 0) {
      console.log(`Access denied to ${pageName}${fieldName ? ` -> ${fieldName}` : ''} due to required permission being 0.`);
      return false;
    }

    // Check roles and permissions
    const hasPermission = rbacRoles.some(role => {
      const menuMatch = role.name === menuName;
      const pageMatch = role.children?.some(child => 
        child.name === pageName &&
        (!fieldName 
          ? hasRequiredPermissions(child.permissions, requiredPermission) 
          : child.children?.some(field => 
              field.name === fieldName &&
              hasRequiredPermissions(field.permissions, requiredPermission)
            )
        )
      );

      return menuMatch && pageMatch;
    });

    // Log permission check results
    if (!hasPermission) {
      console.log(`Access denied: required ${requiredPermission} for ${menuName} -> ${pageName}${fieldName ? ` -> ${fieldName}` : ''}`);
    } else {
      console.log(`Access granted: required ${requiredPermission} for ${menuName} -> ${pageName}${fieldName ? ` -> ${fieldName}` : ''}`);
    }

    return hasPermission;
  }, [rbacRoles]);

  // Wrappers for different access levels
  const canMenuPage = (menuName, pageName, requiredPermission) => canAccess(menuName, pageName, requiredPermission);
  const canMenuPageSection = (menuName, pageName, fieldName, requiredPermission) => canAccess(menuName, pageName, requiredPermission, fieldName);

  // Expose values
  const values = useMemo(() => ({
    rbacRoles,
    canAccessTopMenu,  
    canMenuPage,
    canMenuPageSection,
  }), [rbacRoles, canAccess]);

  return (
    <RBACContext.Provider value={values}>
      {children}
    </RBACContext.Provider>
  );
};

export default RBACContext;
