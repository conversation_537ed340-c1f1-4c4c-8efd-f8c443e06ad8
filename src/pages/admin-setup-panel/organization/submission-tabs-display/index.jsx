import React, { useState } from 'react';
import ApplicantSubmissionTabs from './ApplicantSubmissionTabs';
import BenchApplicantVendorJobBoardTabs from './BenchApplicantVendorJobBoardTabs';
import BenchApplicantRequirementTabs from './BenchApplicantRequirementTabs';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MainCard from 'components/MainCard';

function SubmissionTabsDisplay() {
  const [expanded, setExpanded] = useState('applicant');

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <MainCard title="Applicant Submission">
      <Accordion expanded={expanded === 'applicant'} onChange={handleChange('applicant')} sx={{ mb: 2 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Applicant Submission Tabs</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <ApplicantSubmissionTabs />
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'vendor'} onChange={handleChange('vendor')} sx={{ mb: 2 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Bench Applicant, Vendor, & Job Board Submission Tabs</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <BenchApplicantVendorJobBoardTabs />
        </AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 'requirement'} onChange={handleChange('requirement')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Bench Applicant Requirement Submission Tabs</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <BenchApplicantRequirementTabs />
        </AccordionDetails>
      </Accordion>
    </MainCard>
  );
}

export default SubmissionTabsDisplay;