import React, { useState } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Grid, List, ListItem, ListItemText, Checkbox, IconButton, Paper, Stack } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import DoubleArrowIcon from '@mui/icons-material/KeyboardDoubleArrowRight';
import DoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import Button from '@mui/material/Button';

const initialRight = [
  'Job Code',
  'Job Title',
  'Pipeline Record',
  'Submission Score',
  'Client',
  'Source',
  'Pipeline Status',
  'Client Bill Rate'
];

function AccordionPipelineListViewApplicants({ title = 'Pipeline listing fields for applicants module', defaultExpanded = false }) {
  const [left, setLeft] = useState([]);
  const [right, setRight] = useState(initialRight);
  const [checked, setChecked] = useState([]);
  const [selectedRightIdx, setSelectedRightIdx] = useState(null);

  const leftChecked = checked.filter((item) => left.includes(item));
  const rightChecked = checked.filter((item) => right.includes(item));

  const handleToggle = (value) => () => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setChecked(newChecked);
  };

  const handleCheckedRight = () => {
    const newItems = leftChecked.filter((item) => !right.includes(item));
    setRight([...right, ...newItems]);
    setLeft(left.filter((item) => !leftChecked.includes(item)));
    setChecked(checked.filter((item) => !leftChecked.includes(item)));
  };

  const handleAllRight = () => {
    const newItems = left.filter((item) => !right.includes(item));
    setRight([...right, ...newItems]);
    setLeft([]);
    setChecked(checked.filter((item) => !left.includes(item)));
  };

  const handleCheckedLeft = () => {
    const newItems = rightChecked.filter((item) => !left.includes(item));
    setLeft([...left, ...newItems]);
    setRight(right.filter((item) => !rightChecked.includes(item)));
    setChecked(checked.filter((item) => !rightChecked.includes(item)));
  };

  const handleAllLeft = () => {
    const newItems = right.filter((item) => !left.includes(item));
    setLeft([...left, ...newItems]);
    setRight([]);
    setChecked(checked.filter((item) => !right.includes(item)));
  };

  const moveRightItem = (direction) => {
    if (selectedRightIdx === null || selectedRightIdx < 0 || selectedRightIdx >= right.length) return;
    const newRight = [...right];
    if (direction === 'up' && selectedRightIdx > 0) {
      [newRight[selectedRightIdx - 1], newRight[selectedRightIdx]] = [newRight[selectedRightIdx], newRight[selectedRightIdx - 1]];
      setRight(newRight);
      setSelectedRightIdx(selectedRightIdx - 1);
    } else if (direction === 'down' && selectedRightIdx < newRight.length - 1) {
      [newRight[selectedRightIdx + 1], newRight[selectedRightIdx]] = [newRight[selectedRightIdx], newRight[selectedRightIdx + 1]];
      setRight(newRight);
      setSelectedRightIdx(selectedRightIdx + 1);
    }
  };

  const customList = (items, isRight = false) => (
    <Paper sx={{ width: 260, minHeight: 320, maxHeight: 400, overflow: 'auto', p: 1, borderRadius: 2 }}>
      <List dense component="div" role="list">
        {items.map((value, idx) => {
          const labelId = `transfer-list-item-${value}-label`;
          const isSelected = checked.indexOf(value) !== -1;
          const isRightSelected = isRight && selectedRightIdx === idx;
          return (
            <ListItem
              key={value}
              role="listitem"
              button
              onClick={isRight ? () => setSelectedRightIdx(idx) : handleToggle(value)}
              onDoubleClick={isRight ? handleToggle(value) : undefined}
              selected={isRight ? isRightSelected : isSelected}
              sx={{ borderRadius: 1, mb: 0.5, bgcolor: isRightSelected ? 'primary.lighter' : undefined }}
            >
              <Checkbox
                checked={isSelected}
                tabIndex={-1}
                disableRipple
                color="primary"
                inputProps={{ 'aria-labelledby': labelId }}
                onClick={handleToggle(value)}
              />
              <ListItemText id={labelId} primary={value} sx={{ wordBreak: 'break-word' }} />
            </ListItem>
          );
        })}
      </List>
    </Paper>
  );

  return (
    <Accordion defaultExpanded={defaultExpanded} sx={{ mb: 2 }}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel-content" id="panel-header">
        <Typography variant="subtitle1" fontWeight={600}>{title}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Grid container spacing={2} justifyContent="center" alignItems="flex-start">
          <Grid item>{customList(left)}</Grid>
          <Grid item sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
            <IconButton color="primary" onClick={handleCheckedRight} disabled={leftChecked.length === 0} sx={{ m: 0.5 }}>
              <ArrowForwardIcon />
            </IconButton>
            <IconButton color="primary" onClick={handleAllRight} disabled={left.length === 0} sx={{ m: 0.5 }}>
              <DoubleArrowIcon />
            </IconButton>
            <IconButton color="primary" onClick={handleCheckedLeft} disabled={rightChecked.length === 0} sx={{ m: 0.5 }}>
              <ArrowBackIcon />
            </IconButton>
            <IconButton color="primary" onClick={handleAllLeft} disabled={right.length === 0} sx={{ m: 0.5 }}>
              <DoubleArrowLeftIcon />
            </IconButton>
          </Grid>
          <Grid item>
            {customList(right, true)}
            <Stack spacing={1} alignItems="center" sx={{ mt: 2 }}>
              <IconButton color="primary" onClick={() => moveRightItem('up')} disabled={selectedRightIdx === null || selectedRightIdx === 0}>
                <ArrowUpwardIcon />
              </IconButton>
              <IconButton color="primary" onClick={() => moveRightItem('down')} disabled={selectedRightIdx === null || selectedRightIdx === right.length - 1}>
                <ArrowDownwardIcon />
              </IconButton>
            </Stack>
          </Grid>
        </Grid>
        <Grid container justifyContent="flex-end" sx={{ mt: 2 }}>
          <Button variant="contained" color="primary" onClick={() => console.log('Save:', right)}>
            Save
          </Button>
        </Grid>
      </AccordionDetails>
    </Accordion>
  );
}

export default AccordionPipelineListViewApplicants; 