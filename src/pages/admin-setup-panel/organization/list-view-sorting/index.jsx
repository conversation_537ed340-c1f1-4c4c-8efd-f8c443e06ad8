import React from 'react';
import { Grid, FormHelperText } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import { useForm } from 'react-hook-form';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import AccordionListViewSorting from './AccordionListViewSorting';
import AccordionPipelineListViewSorting from './AccordionPipelineListViewSorting';
import AccordionSubmissionListViewApplicants from './AccordionSubmissionListViewApplicants';
import AccordionPipelineListViewApplicants from './AccordionPipelineListViewApplicants';
import AccordionTalentBenchRequirementSubmissions from './AccordionTalentBenchRequirementSubmissions';
import AccordionTalentBenchVendorSubmissions from './AccordionTalentBenchVendorSubmissions';
import Button from '@mui/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const jobPostingFields = [
  { value: 'Job Code', label: 'Job Code' },
  { value: 'Created On', label: 'Created On' },
  { value: 'Modified On', label: 'Modified On' }
];
const applicantFields = [
  { value: 'Applicant ID', label: 'Applicant ID' },
  { value: 'Created On', label: 'Created On' },
  { value: 'Modified On', label: 'Modified On' }
];
const placementFields = [
  { value: 'Placement ID', label: 'Placement ID' },
  { value: 'Created On', label: 'Created On' },
  { value: 'Modified On', label: 'Modified On' }
];
const orderOptions = [
  { value: 'ASC', label: 'ASC' },
  { value: 'DESC', label: 'DESC' }
];

function ListViewSorting() {
  const { control } = useForm({
    defaultValues: {
      jobPostingField: 'Job Code',
      jobPostingOrder: 'ASC',
      applicantField: 'Applicant ID',
      applicantOrder: 'ASC',
      placementField: 'Placement ID',
      placementOrder: 'ASC'
    }
  });

  return (
    <>
      <MainCard title="List View Sorting">
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Accordion defaultExpanded sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel-content" id="panel-header">
                <CustomInputLabel sx={{ fontSize: 18, fontWeight: 600 }}>List View Sorting Options</CustomInputLabel>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  {/* Job Postings List View */}
                  <Grid item xs={12}>
                    <Grid container spacing={2} alignItems="center" sx={{ borderRadius: 2, p: 2 }}>
                      <Grid item xs={12}>
                        <FormHelperText sx={{ color: 'error.main', fontWeight: 500 }}>
                          (We recommend keeping the Job Code as the default option; performance will be impacted if changed.)
                        </FormHelperText>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomInputLabel>Job Postings List View</CustomInputLabel>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="jobPostingField"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Field"
                          options={jobPostingFields}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="jobPostingOrder"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Order"
                          options={orderOptions}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                  {/* Applicants List View */}
                  <Grid item xs={12}>
                    <Grid container spacing={2} alignItems="center" sx={{ borderRadius: 2, p: 2 }}>
                      <Grid item xs={12}>
                        <FormHelperText sx={{ color: 'error.main', fontWeight: 500 }}>
                          (We recommend keeping the Applicant ID as the default option; performance will be impacted if changed.)
                        </FormHelperText>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomInputLabel>Applicants List View</CustomInputLabel>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="applicantField"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Field"
                          options={applicantFields}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="applicantOrder"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Order"
                          options={orderOptions}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                  {/* Placement List View */}
                  <Grid item xs={12}>
                    <Grid container spacing={2} alignItems="center" sx={{ borderRadius: 2, p: 2 }}>
                      <Grid item xs={12}>
                        <FormHelperText sx={{ color: 'error.main', fontWeight: 500 }}>
                          (We recommend keeping the Placement ID as the default option; performance will be impacted if changed.)
                        </FormHelperText>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomInputLabel>Placement List View</CustomInputLabel>
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="placementField"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Field"
                          options={placementFields}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4} md={3}>
                        <CustomDropdownField
                          name="placementOrder"
                          sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          control={control}
                          placeholder="Select Order"
                          options={orderOptions}
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                  <Grid item xs={12}>
                    <Grid container justifyContent="flex-end" sx={{ mt: 2 }}>
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={() => {
                          const values = control._formValues;
                          console.log('Save List View Sorting:', values);
                        }}
                      >
                        Save
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionListViewSorting />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionPipelineListViewSorting />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionSubmissionListViewApplicants />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionPipelineListViewApplicants />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionTalentBenchRequirementSubmissions />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12}>
              <Grid container spacing={0}>
                <Grid item xs={12}>
                  <AccordionTalentBenchVendorSubmissions />
                </Grid>
              </Grid>
            </Grid>
          </>
        </Grid>
      </MainCard>
    </>
  );
}

export default ListViewSorting;
