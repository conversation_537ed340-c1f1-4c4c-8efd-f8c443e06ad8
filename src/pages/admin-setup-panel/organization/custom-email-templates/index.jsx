import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import ActivityLogTable from './activitylog';
import AddCustomEmailTemplateDialog from './add-custom-email-template';
import EditCustomEmailTemplateDialog from './edit-custom-email-template';
import { useNavigate } from 'react-router-dom';

function CustomEmailTemplates() {
  const [emailTemplates, setEmailTemplates] = useState([
    {
      id: 1,
      template_name: 'sample',
      applicable_module: 'Job Posting',
      email_subject: 'sdjsflds(JobPosting : position_title)',
      status: 'Active',
      reply_to: '<EMAIL>',
      cc: [],
      bcc: [],
      email_body: 'This is a sample email body for job posting.',
    },
    {
      id: 2,
      template_name: 'Sample2',
      applicable_module: 'Leads',
      email_subject: 'Sample Subject',
      status: 'Active',
      reply_to: '<EMAIL>',
      cc: ['Prudhvi Kanmuri', 'Radhika G'],
      bcc: ['Nagaroju', 'Prudhvi Kanmuri', 'Radhika G'],
      email_body: '[LeadContact : contact_first_name]',
    },
  ]);

  const [activityLogs, setActivityLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const navigate = useNavigate();

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleAddSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setEmailTemplates((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        ...data,
        email_subject: data.subject,
        status: 'Active',
      },
    ]);
    setActivityLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'created',
        name: data.template_name,
        user: 'Current User',
        description: `Added email template '${data.template_name}'`,
      },
    ]);
    handleAddDialogClose();
  };

  const handleEditSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setEmailTemplates((prev) =>
      prev.map((template) => (template.id === data.id ? { ...data, email_subject: data.subject, status: 'Active' } : template))
    );
    setActivityLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'updated',
        name: data.template_name,
        user: 'Current User',
        description: `Edited email template '${data.template_name}'`,
      },
    ]);
    setOpenEditDialog(false);
    setSelectedTemplate(null);
  };

  const EmailTemplateActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
      setAnchorEl(null);
    };

    const handleEdit = () => {
      setSelectedTemplate(params.row);
      setOpenEditDialog(true);
      handleClose();
    };

    const handleDelete = () => {
      console.log('Delete email template:', params.row);
      setEmailTemplates((prev) => prev.filter((template) => template.id !== params.row.id));
      setActivityLogs((prev) => [
        ...prev,
        {
          timestamp: new Date().toLocaleString('en-GB'),
          action: 'deleted',
          name: params.row.template_name,
          user: 'Current User',
          description: `Deleted email template '${params.row.template_name}'`,
        },
      ]);
      handleClose();
    };

    const handleStatusChange = () => {
        setEmailTemplates((prev) =>
            prev.map((template) =>
                template.id === params.row.id
                    ? { ...template, status: template.status === 'Active' ? 'Inactive' : 'Active' }
                    : template
            )
        );
        setActivityLogs((prev) => [
            ...prev,
            {
                timestamp: new Date().toLocaleString('en-GB'),
                action: 'status changed',
                name: params.row.template_name,
                user: 'Current User',
                description: `Changed status of '${params.row.template_name}' to ${params.row.status === 'Active' ? 'Inactive' : 'Active'}`,
            },
        ]);
        handleClose();
    };

    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDelete}>Delete</MenuItem>
          <MenuItem onClick={handleStatusChange}>Change Status</MenuItem>
        </Menu>
      </>
    );
  };

  const emailTemplateColumns = [
    {
      field: 'template_name',
      headerName: 'TEMPLATE NAME',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'applicable_module',
      headerName: 'APPLICABLE MODULE',
      flex: 1,
      minWidth: 150,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'email_subject',
      headerName: 'EMAIL SUBJECT',
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'status',
      headerName: 'STATUS',
      flex: 0.5,
      minWidth: 100,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <Typography variant="body2">{params.value}</Typography>
        </Tooltip>
      ),
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <EmailTemplateActionCell params={params} />,
    },
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <MainCard
      title="Custom Email Templates"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
              Back
            </Button>
          </Box>
          <ActivityLogTable logs={activityLogs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
                <Button variant="contained" size="small" color="primary" onClick={handleAddDialogOpen}>
                  + Add Template
                </Button>
                <Button variant="outlined" size="small" onClick={() => navigate('/admin-setup-panel/organization/business-units')}>
                  Back to Business Units
                </Button>
              </Box>
            }
            sx={{ mb: 2 }}
          />
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={emailTemplates}
                  columns={emailTemplateColumns}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={emailTemplates.length}
                />
              </Box>
            </Grid>
          </Grid>
          <AddCustomEmailTemplateDialog
            open={openAddDialog}
            onClose={handleAddDialogClose}
            onSave={handleAddSave}
          />
          <EditCustomEmailTemplateDialog
            open={openEditDialog}
            onClose={() => setOpenEditDialog(false)}
            onSave={handleEditSave}
            emailTemplate={selectedTemplate}
          />
        </>
      )}
    </MainCard>
  );
}

export default CustomEmailTemplates;
