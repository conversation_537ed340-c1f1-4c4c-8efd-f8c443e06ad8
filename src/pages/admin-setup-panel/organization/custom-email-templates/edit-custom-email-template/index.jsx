import React, { useEffect, useState } from 'react';
import { <PERSON>er, Typo<PERSON>, <PERSON>, Divider, IconButton, Button, Grid, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import ReactDraft from 'pages/client-page/component/ReactDraft';
import ApplicantDropzonePage from 'pages/applicant-new/add/DocumentUpload/UploadDocument';

const applicableModuleOptions = [
  { value: 'Job Posting', label: 'Job Posting' },
  { value: 'Applicants', label: 'Applicants' },
  { value: 'Client', label: 'Client' },
  { value: 'Vendors', label: 'Vendors' },
  { value: 'Placements', label: 'Placements' },
  { value: 'Leads', label: 'Leads' },
  { value: 'Submissions', label: 'Submissions' },
];

const availableMergeFieldsOptions = [
  { value: 'Lead', label: 'Lead' },
  { value: 'Lead Contact', label: 'Lead Contact' },
  { value: 'User', label: 'User' },
  { value: 'Organization', label: 'Organization' },
];

const leadContactOptions = [
  { value: 'First Name', label: 'First Name' },
  { value: 'Last Name', label: 'Last Name' },
  { value: 'About Contact', label: 'About Contact' },
  { value: 'Office Number', label: 'Office Number' },
  { value: 'Extension', label: 'Extension' },
  { value: 'Mobile Number', label: 'Mobile Number' },
  { value: 'Email', label: 'Email' },
];

const userOptions = [
    { value: 'Prudhvi Kanmuri', label: 'Prudhvi Kanmuri' },
    { value: 'Radhika G', label: 'Radhika G' },
    { value: 'Nagaraju', label: 'Nagaraju' },
];

const organizationOptions = [
    { value: 'Organization Name', label: 'Organization Name' },
    { value: 'Organization Email', label: 'Organization Email' },
];

const leadOptions = [
  { value: 'Lead ID', label: 'Lead ID' }, // Placeholder options, please define as needed
  { value: 'Lead Name', label: 'Lead Name' },
  { value: 'Lead Source', label: 'Lead Source' },
];

function EditCustomEmailTemplateDialog({ open, onClose, onSave, emailTemplate }) {
  const { control, handleSubmit, reset, setValue, watch } = useForm();

  const selectedMergeField = watch('available_merge_fields');
  const selectedMergeOption = watch(
    selectedMergeField === 'Lead Contact' ? 'lead_contact_field' :
    selectedMergeField === 'User' ? 'user_field' :
    selectedMergeField === 'Organization' ? 'organization_field' :
    selectedMergeField === 'Lead' ? 'lead_field' :
    ''
  );

  const [emailBodyContent, setEmailBodyContent] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (open && emailTemplate) {
      setValue('template_name', emailTemplate.template_name || '');
      setValue('applicable_module', emailTemplate.applicable_module || '');
      setValue('reply_to', emailTemplate.reply_to || '');
      setValue('cc', emailTemplate.cc || []);
      setValue('bcc', emailTemplate.bcc || []);
      setValue('subject', emailTemplate.subject || '');
      setEmailBodyContent(emailTemplate.email_body || '');
      setAttachments(emailTemplate.attachments || []);
    }
  }, [open, emailTemplate, setValue]);

  const handleAddMergeFieldToBody = () => {
    if (selectedMergeField && selectedMergeOption) {
      const mergeTag = `[${selectedMergeField}: ${selectedMergeOption}]`;
      setEmailBodyContent((prev) => {
        const newContent = prev ? prev + mergeTag : mergeTag;
        return newContent;
      });
    }
  };

  const handleAddMergeFieldToSubject = () => {
    if (selectedMergeField && selectedMergeOption) {
      const mergeTag = `[${selectedMergeField}: ${selectedMergeOption}]`;
      setValue('subject', watch('subject') ? watch('subject') + mergeTag : mergeTag, { shouldValidate: true });
    }
  };

  const handleSave = (data) => {
    onSave({
      ...emailTemplate,
      ...data,
      cc: Array.isArray(data.cc) ? data.cc : [],
      bcc: Array.isArray(data.bcc) ? data.bcc : [],
      email_body: emailBodyContent,
      attachments: attachments,
    });
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset();
      setEmailBodyContent('');
      setAttachments([]);
    }
  }, [open, reset]);

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: '90vw', sm: 400 } }
      }}
    >
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Custom Email Template</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <Divider />
        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="template_name">Template Name</CustomInputLabel>
                <CustomNameField
                  name="template_name"
                  control={control}
                  placeholder="Enter Template Name"
                  rules={{ required: 'Template Name is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="applicable_module">Applicable Module</CustomInputLabel>
                <CustomDropdownField
                  name="applicable_module"
                  control={control}
                  placeholder="Select Module"
                  options={applicableModuleOptions}
                  rules={{ required: 'Applicable Module is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="reply_to">Reply To</CustomInputLabel>
                <CustomEmailField
                  name="reply_to"
                  control={control}
                  placeholder="Enter Reply To Email"
                  rules={{ pattern: { value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: 'Invalid email address' } }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="cc">Cc</CustomInputLabel>
                <CustomDropdownField
                  name="cc"
                  control={control}
                  placeholder="Select Cc Recipients"
                  options={userOptions}
                  multiple
                  renderValue={(selected) => selected.join(', ')}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="bcc">Bcc</CustomInputLabel>
                <CustomDropdownField
                  name="bcc"
                  control={control}
                  placeholder="Select Bcc Recipients"
                  options={userOptions}
                  multiple
                  renderValue={(selected) => selected.join(', ')}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="subject">Subject</CustomInputLabel>
                <CustomNameField
                  name="subject"
                  control={control}
                  placeholder="Enter Subject"
                  rules={{ required: 'Subject is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)', flexGrow: 1 }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={0}>
                <CustomInputLabel>Email Body</CustomInputLabel>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={12}>
                    <CustomDropdownField
                      name="available_merge_fields"
                      control={control}
                      placeholder="Select Merge Field"
                      options={availableMergeFieldsOptions}
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={12}>
                    {selectedMergeField === 'Lead' && (
                      <CustomDropdownField
                        name="lead_field"
                        control={control}
                        placeholder="Select Option"
                        options={leadOptions}
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    )}
                    {selectedMergeField === 'Lead Contact' && (
                      <CustomDropdownField
                        name="lead_contact_field"
                        control={control}
                        placeholder="Select Option"
                        options={leadContactOptions}
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    )}
                    {selectedMergeField === 'User' && (
                      <CustomDropdownField
                        name="user_field"
                        control={control}
                        placeholder="Select Option"
                        options={userOptions}
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    )}
                    {selectedMergeField === 'Organization' && (
                      <CustomDropdownField
                        name="organization_field"
                        control={control}
                        placeholder="Select Option"
                        options={organizationOptions}
                        sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                      />
                    )}
                  </Grid>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 1, mb: 2 }}>
                  <Button variant="outlined" onClick={handleAddMergeFieldToBody}>
                    Add Body
                  </Button>
                  <Button variant="outlined" onClick={handleAddMergeFieldToSubject}>
                    Add Subject
                  </Button>
                </Box>
                <ReactDraft value={emailBodyContent} onChange={setEmailBodyContent} placeholder="Enter Email Body" />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel>Attachments</CustomInputLabel>
                <ApplicantDropzonePage
                  name="document"
                  control={control}
                  placeholder="Upload Document"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>
        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={() => setShowPreview(true)} size="small" variant="outlined">
            Preview
          </Button>
          <Button
            onClick={() => {
              reset();
              setEmailBodyContent('');
              setAttachments([]);
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
      {showPreview && (
        <Drawer
          anchor="right"
          open={showPreview}
          onClose={() => setShowPreview(false)}
          PaperProps={{
            sx: { width: { xs: '90vw', sm: 400 } }
          }}
        >
          <Box sx={{ width: '100%', p: 3 }}>
            <Typography variant="h5" mb={2}>
              Email Preview
            </Typography>
            <Typography variant="subtitle1">Subject: {watch('subject')}</Typography>
            <Divider sx={{ my: 2 }} />
            <div dangerouslySetInnerHTML={{ __html: emailBodyContent }} />
            {attachments.length > 0 && (
              <Box mt={3}>
                <Typography variant="subtitle1">Attachments:</Typography>
                <ul>
                  {attachments.map((file, index) => (
                    <li key={index}>{file.name}</li>
                  ))}
                </ul>
              </Box>
            )}
            <Button onClick={() => setShowPreview(false)} variant="outlined" size="small" sx={{ mt: 3 }}>
              Close Preview
            </Button>
          </Box>
        </Drawer>
      )}
    </Drawer>
  );
}

export default EditCustomEmailTemplateDialog; 