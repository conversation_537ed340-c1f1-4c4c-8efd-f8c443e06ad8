import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Box, Button, Grid, IconButton, Menu, MenuItem, Drawer } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import ActivityLogTable from './activitylog';
import EditEmailTemplateForm from './edit-email-template';

export const modules = ['Client Contacts'];

export const mailingDescriptionOptions = {
  'Client Contacts': {
    'User': [
      'First Name', 'Middle Name', 'Display Name', 'Last Name', 'Email',
      'Role', 'Contact Number'
    ],
    'Client Contact': [
      'Contact First Name', 'Contact Last Name', 'Designation', 'About the Contact',
      'Contact Number', 'Extension', 'Mobile Number', 'Email', 'Owners',
      'Primary Ownership', 'Address Line 1', 'Address Line 2', 'Country',
      'State', 'City', 'Zip Code', 'LinkdIn URL', 'Facebook URL', 'Twitter URL',
      'Google Plus URL', 'Status', 'Created By', 'URL', 'Custom Fieldss'
    ],
    'Client': [
      'Name', 'Address', 'Federal ID', 'Email', 'Country', 'State', 'City',
      'Send Requirement', 'Send Hotlist', 'Zip Code', 'Website', 'Fax',
      'Contact Number', 'Industry', 'About Company', 'Status', 'Category',
      'Ownership', 'Primary Owner', 'Created By', 'Required Document',
      'Client Lead', 'Practice', 'Custom Fields'
    ]
  }
};
export const clientContactsTable = {
  columns: [
    { field: 'template_name', headerName: 'TEMPLATE NAME', flex: 1, minWidth: 150 },
    { field: 'email_subject', headerName: 'EMAIL SUBJECT', flex: 1, minWidth: 200 },
    { field: 'templateDescription', headerName: 'EMAIL DESCRIPTION', flex: 1, minWidth: 220 },
    { field: 'status', headerName: 'STATUS', flex: 0.5, minWidth: 100 },
    { field: 'actions', headerName: 'ACTIONS', flex: 0.5, minWidth: 100, sortable: false },
  ],
  rows: [
    {
      id: 1,
      template_name: 'New Client Contact',
      email_subject: 'New contact for [client_name]',
      templateDescription: 'Notification of a new client contact.',
      status: 'Active',
      applicable_module: 'Client Contacts',
      templateSubCategory: 'New Client Contact',
      mailingDescription: 'To Be Filled',
      reply_to: '',
      email_body: 'Notification of a new client contact.',
    },
    {
      id: 2,
      template_name: 'Contact Updated',
      email_subject: 'Client contact updated for [client_name]',
      templateDescription: 'When client contact information changes.',
      status: 'Active',
      applicable_module: 'Client Contacts',
      templateSubCategory: 'Contact Updated',
      mailingDescription: 'To Be Filled',
      reply_to: '',
      email_body: 'When client contact information changes.',
    },
  ],
};


export const ClientContactsEmailTemplates = () => {
  const [activityLogs, setActivityLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [forceUpdate, setForceUpdate] = useState(0);

  // Action cell for demo
  const EmailTemplateActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => { event.stopPropagation(); setAnchorEl(event.currentTarget); };
    const handleClose = () => { setAnchorEl(null); };
    const handleEdit = () => {
      setSelectedTemplate(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleChangeStatusClick = () => {
      handleChangeStatus(params.row);
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleChangeStatusClick}>
            {params.row.status === 'Active' ? 'Set Inactive' : 'Set Active'}
          </MenuItem>
        </Menu>
      </>
    );
  };

  // Add action cell to columns
  const columnsWithActions = () => {
    const baseCols = clientContactsTable.columns || [];
    return baseCols.map(col =>
      col.field === 'actions'
        ? { ...col, renderCell: (params) => <EmailTemplateActionCell params={params} /> }
        : col
    );
  };

  const handleChangeStatus = (row) => {
    // Toggle status in the table
    const updatedRows = clientContactsTable.rows.map(r =>
      r.id === row.id
        ? { ...r, status: r.status === 'Active' ? 'Inactive' : 'Active' }
        : r
    );
    clientContactsTable.rows = updatedRows;

    // Add to activity log
    setActivityLogs(prev => [
      ...prev,
      {
        timestamp: new Date().toLocaleString('en-GB'),
        action: 'status changed',
        name: row.template_name,
        user: 'Current User',
        description: `Changed status of '${row.template_name}' in Client Contacts to ${row.status === 'Active' ? 'Inactive' : 'Active'}`,
      }
    ]);

    setForceUpdate(x => x + 1);
  };

  const handleEditSave = (formData, activityLog) => {
    // Update the email template in the category table
    if (selectedTemplate) {
      const updatedRows = clientContactsTable.rows.map(r =>
        r.id === selectedTemplate.id
          ? { ...r, ...formData }
          : r
      );
      clientContactsTable.rows = updatedRows;
    }
    if (activityLog) {
      setActivityLogs((prev) => [activityLog, ...prev]);
    }
    setOpenEditDialog(false);
    setSelectedTemplate(null);
    setForceUpdate(x => x + 1);
  };

  return (
    <MainCard
      title="Client Contacts Email Templates"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
              Back
            </Button>
          </Box>
          <ActivityLogTable logs={activityLogs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
              </Box>
            }
            sx={{ mb: 2 }}
          />

          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={clientContactsTable.rows}
                  columns={columnsWithActions()}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={clientContactsTable.rows.length}
                />
              </Box>
            </Grid>
          </Grid>
          <Drawer
            anchor="right"
            open={openEditDialog}
            onClose={() => setOpenEditDialog(false)}
            PaperProps={{
              sx: { 
                width: { 
                  xs: '100%',
                  sm: '80%',
                  md: '70%',
                  lg: '60%'
                },
                '& .MuiDrawer-paper': {
                  overflowX: 'hidden'
                }
              }
            }}
          >
            <EditEmailTemplateForm
              initialData={selectedTemplate}
              onSave={handleEditSave}
              onCancel={() => setOpenEditDialog(false)}
              modules={modules}
              mailingDescriptionOptions={mailingDescriptionOptions}
            />
          </Drawer>
        </>
      )}
    </MainCard>
  );
};

export default ClientContactsEmailTemplates;
