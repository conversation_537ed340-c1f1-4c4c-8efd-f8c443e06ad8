import React, { useState, useEffect } from 'react';
import { <PERSON>po<PERSON>, <PERSON>, Grid, Button, Checkbox, FormControlLabel, <PERSON>er, Divider, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomN<PERSON>Field from 'components/custom-components/CustomNameField';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomEmailField from 'components/custom-components/CustomEmailField';
import ReactDraft from 'pages/client-page/component/ReactDraft';

// Tag mapping for modules and subcategories
const moduleTagMap = {
  Leads: {
    Lead: 'Lead',
    User: 'User',
    'Lead Note': 'LeadNote'
  },
  'Job Posting': 'JobPosting',
  'Job Requisition': 'Requisition',
  'Careers Portal': {
    'Job Posting': 'JobPosting',
    'Job Seekers': 'JobSeeker',
    'Job Application': 'JobApplication',
    'Business Units': 'BusinessUnit'
  },
  'Applicant Profile': {
    'Job Seekers': 'JobSeeker',
    'Job Posting': 'JobPosting',
    'Job Application': 'JobApplication'
  },
  TalentBench: {
    'Job Seekers': 'JobSeeker',
    'Submission Details': 'Submission'
  },
  Client: 'Client',
  Vendor: 'Vendor',
  Placements: {
    'Placement Details': 'Placement',
    'Applicant Details': 'Applicant',
    'Job Posting': 'JobPosting',
    'Job Seekers': 'JobSeeker'
  },
  Submissions: {
    'Job Seekers': 'JobSeeker',
    'Job Application': 'JobApplication',
    'Job Posting': 'JobPosting',
    Requisitions: 'Requisition',
    User: 'User',
    'Submission Details': 'Submission'
  },
  Users: 'User',
  'Client Contacts': {
    User: 'User',
    'Client Contact': 'ClientContact',
    Client: 'Client'
  },
  'Vendor Contacts': {
    User: 'User',
    Vendors: 'Vendor',
    'Vendor Contacts': 'VendorContact'
  },
  'Referral Portal': {
    'Referral Employee': 'ReferralEmployee',
    'Job Posting': 'JobPosting',
    'Job Seekers': 'JobSeeker'
  }
};

function getTagLabel(module, subcategory) {
  const tag = moduleTagMap[module];
  if (typeof tag === 'string') return tag;
  if (typeof tag === 'object' && subcategory && tag[subcategory]) return tag[subcategory];
  return module;
}

const EditEmailTemplateDrawer = ({ open, onClose, initialData, onSave, modules, mailingDescriptionOptions }) => {
  const { control, setValue, handleSubmit, watch, reset } = useForm();
  const [enableTemplate, setEnableTemplate] = useState(false);
  const [emailBodyContent, setEmailBodyContent] = useState('');

  // Watch fields for controlled state
  const selectedModule = watch('applicable_module') || initialData?.applicable_module || '';
  const selectedMailing1 = watch('mailing1') || '';
  const selectedMailing2 = watch('mailing2') || '';

  useEffect(() => {
    if (initialData) {
      reset({
        template_name: initialData.template_name || '',
        applicable_module: initialData.applicable_module || '',
        templateSubCategory: initialData.templateSubCategory || '',
        mailing1: '',
        mailing2: '',
        templateDescription: initialData.templateDescription || '',
        reply_to: initialData.reply_to || '',
        email_subject: initialData.email_subject || ''
      });
      setEnableTemplate(initialData.status === 'Active');
      setEmailBodyContent(initialData.email_body || '');
    } else {
      reset({
        template_name: '',
        applicable_module: '',
        templateSubCategory: '',
        mailing1: '',
        mailing2: '',
        templateDescription: '',
        reply_to: '',
        email_subject: ''
      });
      setEnableTemplate(false);
      setEmailBodyContent('');
    }
  }, [initialData, reset]);

  // Two-level cascading dropdown options
  const mailingOptions1 = Object.keys(mailingDescriptionOptions[selectedModule] || {});
  const mailingOptions2 = selectedMailing1 ? mailingDescriptionOptions[selectedModule]?.[selectedMailing1] || [] : [];

  const handleSaveClick = (data) => {
    const formData = {
      ...data,
      status: enableTemplate ? 'Active' : 'Inactive',
      email_body: emailBodyContent,
      ...(initialData && { id: initialData.id })
    };

    // Create activity log
    const activityLog = {
      timestamp: new Date().toLocaleString('en-GB'),
      action: initialData ? 'edited' : 'created',
      name: data.template_name,
      user: 'Current User',
      description: `${initialData ? 'Edited' : 'Created'} email template '${data.template_name}' in ${data.applicable_module}`
    };

    onSave(formData, activityLog);
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: '90vw', sm: 500 } }
      }}
    >
      <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">
              {initialData ? `Edit Email Template: ${initialData.template_name}` : 'Add New Email Template'}
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <Divider />
        {/* Form */}
        <Box sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <form id="edit-email-template-form" onSubmit={handleSubmit(handleSaveClick)}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <CustomInputLabel htmlFor="applicable_module">Choose Module</CustomInputLabel>
                <CustomDropdownField
                  name="applicable_module"
                  control={control}
                  placeholder="Select Module"
                  options={modules.map(moduleName => ({ value: moduleName }))}
                  rules={{ required: 'Module is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              <Grid item xs={12}>
                <CustomInputLabel htmlFor="templateSubCategory">Template Sub Category</CustomInputLabel>
                <CustomNameField
                  name="templateSubCategory"
                  control={control}
                  placeholder="Enter Template Sub Category"
                  rules={{ required: 'Template Sub Category is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              <Grid item xs={12}>
                <CustomInputLabel htmlFor="templateDescription">Template Description</CustomInputLabel>
                <CustomNameField
                  name="templateDescription"
                  control={control}
                  placeholder="Enter Template Description"
                  multiline
                  rows={4}
                  rules={{ required: 'Template Description is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              <Grid item xs={12}>
                <CustomInputLabel htmlFor="reply_to">Reply To</CustomInputLabel>
                <CustomEmailField
                  name="reply_to"
                  control={control}
                  placeholder="Enter Reply To"
                  rules={{ required: 'Reply To is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              {/* Mailing Description and Add Body (Grid 1) */}
              <Grid item xs={12}>
                <Box display="flex" alignItems="center" gap={2} sx={{ mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <CustomInputLabel htmlFor="mailing1">Mailing Description</CustomInputLabel>
                    <CustomDropdownField
                      name="mailing1"
                      control={control}
                      placeholder="Select"
                      options={mailingOptions1.map((opt) => ({ value: opt }))}
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Box>
                  <Button
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => {
                      const tagLabel = getTagLabel(selectedModule, selectedMailing1);
                      const tag = `{${tagLabel} : ${selectedMailing2}}`;
                      setEmailBodyContent((prev) => (prev ? prev + tag : tag));
                    }}
                  >
                    Add Body
                  </Button>
                </Box>
              </Grid>

              {/* Select Options and Add Subject (Grid 2) */}
              <Grid item xs={12}>
                <Box display="flex" alignItems="center" gap={2} sx={{ mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <CustomInputLabel htmlFor="mailing2">Select Options</CustomInputLabel>
                    <CustomDropdownField
                      name="mailing2"
                      control={control}
                      placeholder="Select"
                      options={mailingOptions2.map((opt) => ({ value: opt }))}
                      sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  </Box>
                  <Button
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => {
                      const tagLabel = getTagLabel(selectedModule, selectedMailing1);
                      setValue('email_subject', (watch('email_subject') || '') + `{${tagLabel} : ${selectedMailing2}}`);
                    }}
                  >
                    Add Subject
                  </Button>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <CustomInputLabel htmlFor="email_subject">Mailing Subject *</CustomInputLabel>
                <CustomNameField
                  name="email_subject"
                  control={control}
                  placeholder="Enter Mailing Subject"
                  rules={{ required: 'Mailing Subject is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={enableTemplate}
                      onChange={(e) => setEnableTemplate(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Enable this email template"
                />
              </Grid>

              <Grid item xs={12}>
                <CustomInputLabel htmlFor="email_body">Email Body</CustomInputLabel>
                <ReactDraft
                  value={emailBodyContent}
                  onChange={setEmailBodyContent}
                  placeholder="Enter Email Body"
                />
              </Grid>
            </Grid>
          </form>
        </Box>
        <Divider />
        {/* Footer */}
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button onClick={onClose} size="small" variant="outlined">Cancel</Button>
          <Button type="submit" size="small" variant="contained" color="primary" form="edit-email-template-form">Save</Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default EditEmailTemplateDrawer;
