import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Box, Button, Grid, IconButton, Menu, MenuItem, Drawer } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MainCard from 'components/MainCard';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import ActivityLogTable from './activitylog';
import EditEmailTemplateForm from './edit-email-template';

export const modules = ['Applicant Profile'];

export const mailingDescriptionOptions = {
  'Applicant Profile': {
    'Job Seekers': [
      'Applicant ID', 'First Name', 'Last Name', 'Middle Name', 'Nick Name',
      'Consultant Name', 'SSN', 'Email Address', 'Alternate Email Address',
      'Home Phone Number', 'Work Phone Number', 'Other Phone', 'Date Of Birth',
      'Work Authorization', 'Skype ID', 'Source', 'Expected Pay', 'Address',
      'City', 'State', 'Country', 'Zip Code', 'LinkdIn Profile URL',
      'Facebook Profile URL', 'Twitter Profile URL', 'Video Reference',
      'Job Title', 'Skills', 'Experience', 'Gender', 'Race/Ethnicity',
      'Veteran Type', 'Disability', 'URL', 'Mobile Number', 'Ownership',
      'Favorite', 'Sales Team Member', 'Sell Rate', 'Consultant Type',
      'Location Preference', 'Availability', 'Application Status', 'Referred By',
      'Priority', 'Account Manager', 'Relocation', 'Preferred Location',
      'Clearance Type', 'Primary Skills', 'Lead Type', 'Lead Generated By',
      'Time to Place the Consultant', 'Technology', 'Tax Terms', 'Comments',
      'Additional Comments', 'Veteran Status', 'Notice Period', 'Current CTC',
      'Current Company', 'Pan Card Number', 'Aadhar Number', 'School Name',
      'Degree', 'Year Completed', 'City', 'State', 'Country',
      'Higher Education Deatils', 'Degree', 'School Name', 'Year Completed',
      'City', 'State', 'CountryCountry', 'Custom Fields'
    ],
    'Job Posting': [
      'Position Title', 'Posted By', 'Respond By', 'Number of Positions',
      'Recruitment Manager', 'Industry', 'State', 'Country', 'Address',
      'Remote Job', 'Job Status', 'Client Bill Rate', 'Tax Terms', 'Pay Rate',
      'Comments', 'Job Description', 'Public Job Description', 'Public Job Title',
      'Job Code', 'Required Hours/Week', 'Turnaround Time', 'Department', 'Degree',
      'Assigned To', 'Sales Manager', 'Experience', 'Required Documents',
      'Evalution Template', 'Primary Skills', 'Languages', 'URL', 'Created On',
      'Career Portal Published Date', 'Client', 'Client Manager', 'End Client',
      'Priority', 'Duration', 'Work Authorization', 'Client Job ID', 'Client Category',
      'Secondary Skills', 'Expenses Paid', 'Interview Mode', 'Max No Of Submissions',
      'Primary Recruiter', 'Job Start Date', 'Job End Date', 'Skill Matrix',
      'Clearance', 'Account Manager', 'Job Type', 'Location', 'Document Attachments',
      'Job Age', 'Custom Fields'
    ],
    'Job Application': [
      'Submission ID', 'Job Seeker ID', 'Date Available', 'Pay Rate/Salary',
      'Tax Terms', 'Location Preference', 'Submission Comments', 'Comments',
      'Video Link', 'Attach Resume', 'Bill Rate/Salary', 'Technical Skills',
      'Communication Skills', 'Professionalism', 'Overall Rating', 'Submission URL',
      'Submitted On', 'Pay Rate', 'Status Change Comments', 'Submitted By',
      'CustomField'
    ]
  }
};

export const applicantProfileTable = {
  columns: [
    { field: 'template_name', headerName: 'TEMPLATE NAME', flex: 1, minWidth: 150 },
    { field: 'email_subject', headerName: 'EMAIL SUBJECT', flex: 1, minWidth: 200 },
    { field: 'templateDescription', headerName: 'EMAIL DESCRIPTION', flex: 1, minWidth: 220 },
    { field: 'status', headerName: 'STATUS', flex: 0.5, minWidth: 100 },
    { field: 'actions', headerName: 'ACTIONS', flex: 0.5, minWidth: 100, sortable: false },
  ],
  rows: [
    {
      id: 1,
      template_name: 'Profile Updated',
      email_subject: 'Your profile has been updated',
      templateDescription: 'Sent when an applicant updates their profile.',
      status: 'Active',
      applicable_module: 'Applicant Profile',
      templateSubCategory: 'Profile Updated',
      mailingDescription: 'To Be Filled',
      reply_to: '',
      email_body: 'Sent when an applicant updates their profile.',
    },
    {
      id: 2,
      template_name: 'Document Uploaded',
      email_subject: 'New document added to your profile',
      templateDescription: 'Notification for document uploads.',
      status: 'Active',
      applicable_module: 'Applicant Profile',
      templateSubCategory: 'Document Uploaded',
      mailingDescription: 'To Be Filled',
      reply_to: '',
      email_body: 'Notification for document uploads.',
    },
  ],
};


export const ApplicantProfileEmailTemplates = () => {
  const [activityLogs, setActivityLogs] = useState([]);
  const [showActivities, setShowActivities] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });
  const [forceUpdate, setForceUpdate] = useState(0);

  
  // Action cell for demo
  const EmailTemplateActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => { event.stopPropagation(); setAnchorEl(event.currentTarget); };
    const handleClose = () => { setAnchorEl(null); };
    const handleEdit = () => {
      setSelectedTemplate(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleChangeStatusClick = () => {
      handleChangeStatus(params.row);
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleChangeStatusClick}>
            {params.row.status === 'Active' ? 'Set Inactive' : 'Set Active'}
          </MenuItem>
        </Menu>
      </>
    );
  };

  // Add action cell to columns
  const columnsWithActions = () => {
    const baseCols = applicantProfileTable.columns || [];
    return baseCols.map(col =>
      col.field === 'actions'
        ? { ...col, renderCell: (params) => <EmailTemplateActionCell params={params} /> }
        : col
    );
  };

  const handleChangeStatus = (row) => {
    // Toggle status in the table
    const updatedRows = applicantProfileTable.rows.map(r =>
      r.id === row.id
        ? { ...r, status: r.status === 'Active' ? 'Inactive' : 'Active' }
        : r
    );
    applicantProfileTable.rows = updatedRows;

    // Add to activity log
    setActivityLogs(prev => [
      ...prev,
      {
        timestamp: new Date().toLocaleString('en-GB'),
        action: 'status changed',
        name: row.template_name,
        user: 'Current User',
        description: `Changed status of '${row.template_name}' in Applicant Profile to ${row.status === 'Active' ? 'Inactive' : 'Active'}`,
      }
    ]);

    setForceUpdate(x => x + 1);
  };

  const handleEditSave = (formData, activityLog) => {
    // Update the email template in the category table
    if (selectedTemplate) {
      const updatedRows = applicantProfileTable.rows.map(r =>
        r.id === selectedTemplate.id
          ? { ...r, ...formData }
          : r
      );
      applicantProfileTable.rows = updatedRows;
    }
    if (activityLog) {
      setActivityLogs((prev) => [activityLog, ...prev]);
    }
    setOpenEditDialog(false);
    setSelectedTemplate(null);
    setForceUpdate(x => x + 1);
  };

  return (
    <MainCard
      title="Applicant Profile Email Templates"
      sx={{ borderRadius: '1%', backgroundColor: 'white', '& .MuiInputLabel-root': { fontSize: '0.875rem' }, mt: 0.2 }}
    >
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
              Back
            </Button>
          </Box>
          <ActivityLogTable logs={activityLogs} />
        </>
      ) : (
        <>
          <CustomCardHeader
            secondary={
              <Box display="flex" alignItems="center" gap={1}>
                <Button variant="outlined" size="small" onClick={() => setShowActivities(true)}>
                  Activities
                </Button>
              </Box>
            }
            sx={{ mb: 2 }}
          />

          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12}>
              <Box sx={{ height: 300, width: '100%' }}>
                <CustomDataGrid
                  rows={applicantProfileTable.rows}
                  columns={columnsWithActions()}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={applicantProfileTable.rows.length}
                />
              </Box>
            </Grid>
          </Grid>
          <Drawer
            anchor="right"
            open={openEditDialog}
            onClose={() => setOpenEditDialog(false)}
            PaperProps={{
              sx: { 
                width: { 
                  xs: '100%',
                  sm: '80%',
                  md: '70%',
                  lg: '60%'
                },
                '& .MuiDrawer-paper': {
                  overflowX: 'hidden'
                }
              }
            }}
          >
            <EditEmailTemplateForm
              initialData={selectedTemplate}
              onSave={handleEditSave}
              onCancel={() => setOpenEditDialog(false)}
              modules={modules}
              mailingDescriptionOptions={mailingDescriptionOptions}
            />
          </Drawer>
        </>
      )}
    </MainCard>
  );
};

export default ApplicantProfileEmailTemplates;
