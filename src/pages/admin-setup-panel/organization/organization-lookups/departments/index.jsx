import React, { useState } from 'react';
import { Box, Button, IconButton, Menu, MenuItem} from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import AddDepartmentDialog from './adddepartments';
import EditDepartmentDialog from './editdepartments';
import ActivityLogTable from './activitylog';

function Departments() {

  const columns = [
    {
      field: 'department',
      headerName: 'DEPARTMENT',
      flex: 1,
      minWidth: 150,
      renderHeader: () => (
        <Box display="flex" alignItems="center" gap={1}>
          <CustomInputLabel sx={{ fontWeight: 'bold', color: 'black' }}>DEPARTMENT</CustomInputLabel>
          <Box component="span" sx={{ fontSize: '12px' }}>↕</Box>
        </Box>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => params.row.created_by || 'Prudhvi <PERSON>nmuri'
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => params.row.modified_by || 'Prudhvi Kanmuri'
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 140,
      renderHeader: () => (
        <Box display="flex" alignItems="center" gap={1}>
          <CustomInputLabel sx={{ fontWeight: 'bold', color: 'black'}}>LAST MODIFIED</CustomInputLabel>
          <Box component="span" sx={{ fontSize: '12px' }}>↕</Box>
        </Box>
      ),
      renderCell: (params) => params.row.last_modified || '12/09/23 11:46:26'
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ActionCell params={params} />
    }
  ];

  // Sample data matching the image
  const [rows, setRows] = useState([
    {
      id: 1,
      department: 'Technology',
      created_by: 'Prudhvi Kanmuri',
      modified_by: 'Prudhvi Kanmuri',
      last_modified: '12/09/23 11:46:26'
    }
  ]);

  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [showActivities, setShowActivities] = useState(false);
  const [logs, setLogs] = useState([]);

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleAddSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        department: data.department,
        created_by: 'Prudhvi Kanmuri',
        modified_by: 'Prudhvi Kanmuri',
        last_modified: now
      }
    ]);
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'created',
        name: data.department,
        user: 'Current User',
        description: `Added department: ${data.department}`
      }
    ]);
    handleAddDialogClose();
  };
  const handleEditSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => prev.map((row) =>
      row.id === data.id ? {
        ...row,
        department: data.department,
        modified_by: 'Current User',
        last_modified: now
      } : row
    ));
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'updated',
        name: data.department,
        user: 'Current User',
        description: `Edited department: ${data.department}`
      }
    ]);
    setOpenEditDialog(false);
    setSelectedRow(null);
  };

  const handleDelete = (row) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => prev.filter((r) => r.id !== row.id));
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'deleted',
        name: row.department,
        user: 'Current User',
        description: `Deleted department: ${row.department}`
      }
    ]);
  };
  const ActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => setAnchorEl(null);
    const handleEdit = () => {
      setSelectedRow(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleDeleteClick = () => {
      handleDelete(params.row);
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDeleteClick}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <Box sx={{ p: 0 }}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
              Back
            </Button>
          </Box>
          <ActivityLogTable logs={logs} />
        </>
      ) : (
        <>
          <Box display="flex" justifyContent="flex-end" alignItems="center" mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setShowActivities(true)}
                sx={{
                  fontSize: '0.875rem',
                  px: 2
                }}
              >
                Activities
              </Button>
              <Button
                variant="contained"
                size="small"
                color="primary"
                onClick={handleAddDialogOpen}
                sx={{
                  fontSize: '0.875rem',
                  px: 2
                }}
              >
                + Add
              </Button>
            </Box>
          </Box>

          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={rows}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={rows.length}
            />
          </Box>

          <AddDepartmentDialog
            open={openAddDialog}
            onClose={handleAddDialogClose}
            onSave={handleAddSave}
          />
          <EditDepartmentDialog
            open={openEditDialog}
            onClose={() => setOpenEditDialog(false)}
            onSave={handleEditSave}
            selectedRow={selectedRow}
          />
        </>
      )}
    </Box>
  );
}

export default Departments;