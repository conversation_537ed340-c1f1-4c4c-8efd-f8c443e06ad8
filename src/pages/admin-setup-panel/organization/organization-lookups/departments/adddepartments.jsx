import React, { useEffect } from 'react';
import { Drawer, Typography, Box, Divider, IconButton, Button, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function AddDepartmentDialog({ open, onClose, onSave }) {
  const { control, handleSubmit, reset } = useForm();

  const handleSave = (data) => {
    onSave({
      department: data.department
    });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: 300, md: 400 },
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Add Department</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Stack spacing={3}>
            <Stack spacing={1}>
              <CustomInputLabel htmlFor="department">Department Name</CustomInputLabel>
              <CustomNameField
                name="department"
                control={control}
                placeholder="Enter department name"
                rules={{ required: 'Department name is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Stack>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Add
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default AddDepartmentDialog;
