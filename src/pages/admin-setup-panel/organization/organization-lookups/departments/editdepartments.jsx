import React, { useEffect } from 'react';
import { Drawer, Typography, Box, Divider, IconButton, Button, Stack } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function EditDepartmentDialog({ open, onClose, onSave, selectedRow }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && selectedRow) {
      setValue('department', selectedRow.department || '');
    }
  }, [open, selectedRow, setValue]);

  const handleSave = (data) => {
    onSave({
      ...selectedRow,
      department: data.department
    });
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: 300, md: 400 },
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Department</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Stack spacing={3}>
            <Stack spacing={1}>
              <CustomInputLabel htmlFor="department">Department Name</CustomInputLabel>
              <CustomNameField
                name="department"
                control={control}
                placeholder="Enter department name"
                rules={{ required: 'Department name is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>
          </Stack>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditDepartmentDialog;
