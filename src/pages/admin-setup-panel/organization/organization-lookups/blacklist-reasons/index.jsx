import React, { useState } from 'react';
import { Box, Button, IconButton, Menu, MenuItem } from '@mui/material';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import AddBlacklistReasonDialog from './addblacklist-reasons';
import EditBlacklistReasonDialog from './editblacklist-reasons';
import ActivityLogTable from './activitylog';

function BlacklistReasons() {

  const columns = [
    {
      field: 'reason',
      headerName: 'REASONS',
      flex: 1,
      minWidth: 150,
      renderHeader: () => (
        <Box display="flex" alignItems="center" gap={1}>
          <CustomInputLabel sx={{ fontWeight: 'bold', color: 'black' }}>REASONS</CustomInputLabel>
          <Box component="span" sx={{ fontSize: '12px' }}>↕</Box>
        </Box>
      )
    },
    {
      field: 'applicable_to',
      headerName: 'APPLICABLE TO',
      flex: 1,
      minWidth: 180,
      renderHeader: () => (
        <Box display="flex" alignItems="center" gap={1}>
          < CustomInputLabel sx={{ fontWeight: 'bold', color: 'black' }}>APPLICABLE TO</ CustomInputLabel>
          <Box component="span" sx={{ fontSize: '12px' }}>↕</Box>
        </Box>
      )
    },
    {
      field: 'created_by',
      headerName: 'CREATED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => params.row.created_by || 'system'
    },
    {
      field: 'modified_by',
      headerName: 'MODIFIED BY',
      flex: 1,
      minWidth: 120,
      renderCell: (params) => params.row.modified_by || 'N/A'
    },
    {
      field: 'last_modified',
      headerName: 'LAST MODIFIED',
      flex: 1,
      minWidth: 140,
      renderHeader: () => (
        <Box display="flex" alignItems="center" gap={1}>
          <CustomInputLabel sx={{ fontWeight: 'bold', color: 'black' }}>LAST MODIFIED</CustomInputLabel>
          <Box component="span" sx={{ fontSize: '12px' }}>↕</Box>
        </Box>
      ),
      renderCell: (params) => params.row.last_modified || '12/12/23 17:03:05'
    },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => <ActionCell params={params} />
    }
  ];

  // Sample data matching the image
  const [rows, setRows] = useState([
    {
      id: 1,
      reason: 'Others',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 2,
      reason: 'Not being Polite',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 3,
      reason: 'No Interview schedules',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 4,
      reason: 'Fake Requirements',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 5,
      reason: 'Not giving proper feedbacks',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 6,
      reason: 'Incomplete Requirement',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    },
    {
      id: 7,
      reason: 'Others',
      applicable_to: 'Client Contact & Vendor Contact',
      created_by: 'system',
      modified_by: 'N/A',
      last_modified: '12/12/23 17:03:05'
    }
  ]);

  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [showActivities, setShowActivities] = useState(false);
  const [logs, setLogs] = useState([]);

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);
  const handleAddSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        reason: data.reason,
        applicable_to: data.applicable_to,
        created_by: 'system',
        modified_by: 'N/A',
        last_modified: now
      }
    ]);
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'created',
        name: data.reason,
        user: 'Current User',
        description: `Added blacklist reason: ${data.reason}`
      }
    ]);
    handleAddDialogClose();
  };
  const handleEditSave = (data) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => prev.map((row) =>
      row.id === data.id ? {
        ...row,
        reason: data.reason,
        applicable_to: data.applicable_to,
        modified_by: 'Current User',
        last_modified: now
      } : row
    ));
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'updated',
        name: data.reason,
        user: 'Current User',
        description: `Edited blacklist reason: ${data.reason}`
      }
    ]);
    setOpenEditDialog(false);
    setSelectedRow(null);
  };

  const handleDelete = (row) => {
    const now = new Date().toLocaleString('en-GB');
    setRows((prev) => prev.filter((r) => r.id !== row.id));
    setLogs((prev) => [
      ...prev,
      {
        timestamp: now,
        action: 'deleted',
        name: row.reason,
        user: 'Current User',
        description: `Deleted blacklist reason: ${row.reason}`
      }
    ]);
  };
  const ActionCell = ({ params }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleMenuClick = (event) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = () => setAnchorEl(null);
    const handleEdit = () => {
      setSelectedRow(params.row);
      setOpenEditDialog(true);
      handleClose();
    };
    const handleDeleteClick = () => {
      handleDelete(params.row);
      handleClose();
    };
    return (
      <>
        <IconButton onClick={handleMenuClick}>
          <MoreVertIcon />
        </IconButton>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEdit}>Edit</MenuItem>
          <MenuItem onClick={handleDeleteClick}>Delete</MenuItem>
        </Menu>
      </>
    );
  };

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <Box sx={{ p: 0 }}>
      {showActivities ? (
        <>
          <Box display="flex" justifyContent="flex-end" mb={2}>
            <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
              Back
            </Button>
          </Box>
          <ActivityLogTable logs={logs} />
        </>
      ) : (
        <>
          <Box display="flex" justifyContent="flex-end" alignItems="center" mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setShowActivities(true)}
                sx={{
                  fontSize: '0.875rem',
                  px: 2
                }}
              >
                Activities
              </Button>
              <Button
                variant="contained"
                size="small"
                color="primary"
                onClick={handleAddDialogOpen}
                sx={{
                  fontSize: '0.875rem',
                  px: 2
                }}
              >
                + Add
              </Button>
            </Box>
          </Box>

          <Box sx={{ height: 400, width: '100%' }}>
            <CustomDataGrid
              rows={rows}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={rows.length}
            />
          </Box>

          <AddBlacklistReasonDialog
            open={openAddDialog}
            onClose={handleAddDialogClose}
            onSave={handleAddSave}
          />
          <EditBlacklistReasonDialog
            open={openEditDialog}
            onClose={() => setOpenEditDialog(false)}
            onSave={handleEditSave}
            selectedRow={selectedRow}
          />
        </>
      )}
    </Box>
  );
}

export default BlacklistReasons;