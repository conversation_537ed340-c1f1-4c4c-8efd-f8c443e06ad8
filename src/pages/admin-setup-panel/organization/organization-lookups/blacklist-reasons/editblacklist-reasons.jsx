import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Typo<PERSON>, <PERSON>, Divider, IconButton, Button, Stack, FormControlLabel, Checkbox } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function EditBlacklistReasonDialog({ open, onClose, onSave, selectedRow }) {
  const { control, handleSubmit, reset, setValue, register } = useForm();

  useEffect(() => {
    if (open && selectedRow) {
      setValue('reason', selectedRow.reason || '');
      // Parse applicable_to to set checkboxes
      const applicableTo = selectedRow.applicable_to || '';
      setValue('applicant', applicableTo.includes('Applicant'));
      setValue('client_vendor', applicableTo.includes('Client & Vendor'));
      setValue('client_vendor_contact', applicableTo.includes('Client Contact & Vendor Contact'));
    }
  }, [open, selectedRow, setValue]);

  const handleSave = (data) => {
    const applicableToArray = [];
    if (data.applicant) applicableToArray.push('Applicant');
    if (data.client_vendor) applicableToArray.push('Client & Vendor');
    if (data.client_vendor_contact) applicableToArray.push('Client Contact & Vendor Contact');

    onSave({
      ...selectedRow,
      reason: data.reason,
      applicable_to: applicableToArray.join(', ')
    });
    onClose();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box
        sx={{
          width: { xs: 300, md: 400 },
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Blacklist Reason</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Stack spacing={3}>
            <Stack spacing={1}>
              <CustomInputLabel htmlFor="reason">Reason</CustomInputLabel>
              <CustomNameField
                name="reason"
                control={control}
                placeholder="Enter reason"
                rules={{ required: 'Reason is required' }}
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
              />
            </Stack>

            <Stack spacing={2}>
              <CustomInputLabel>Applicable To</CustomInputLabel>
              <FormControlLabel control={<Checkbox name="applicant" {...register('applicant')} color="primary" />} label="Applicant" />
              <FormControlLabel
                control={<Checkbox name="client_vendor" {...register('client_vendor')} color="primary" />}
                label="Client & Vendor"
              />
              <FormControlLabel
                control={<Checkbox name="client_vendor_contact" {...register('client_vendor_contact')} color="primary" />}
                label="Client Contact & Vendor Contact"
              />
            </Stack>
          </Stack>
        </Box>

        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditBlacklistReasonDialog;
