import React, { useState } from 'react';
import { Box, Tabs, Tab, Typography, Card } from '@mui/material';
import MainCard from 'components/MainCard';
import BlacklistReasons from './blacklist-reasons';
import Departments from './departments';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function OrganizationLookups() {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <MainCard
      sx={{
        borderRadius: '0%',
        backgroundColor: 'white',
        '& .MuiInputLabel-root': { fontSize: '0.875rem' }
      }}
    >
      <Box
        sx={{
          mt: -2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}
      >
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="organization lookups tabs">
          <Tab label="Blacklist Reasons" {...a11yProps(0)} />
          <Tab label="Departments" {...a11yProps(1)} />
        </Tabs>
      </Box>

      <TabPanel value={selectedTab} index={0}>
        <BlacklistReasons />
      </TabPanel>
      <TabPanel value={selectedTab} index={1}>
        <Departments />
      </TabPanel>
    </MainCard>
  );
}

export default OrganizationLookups;
