import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller, FormProvider } from 'react-hook-form';
import {
  Box,
  Button,
  Grid,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  IconButton,
  Checkbox,
  Divider,
  Stack,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDateOfBirth from 'components/custom-components/CustomDateOfBirth';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MainCard from 'components/MainCard';
import CustomFormHelperText from 'components/custom-components/CustomFormHelperText';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';

function TargetSettings() {
  const theme = useTheme();
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isLaptop = useMediaQuery(theme.breakpoints.between('md', 'lg'));

  // State for activity log toggle
  const [showActivities, setShowActivities] = useState(false);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10
  });

  // Activity logs
  const [activityLogs, setActivityLogs] = useState([
    {
      id: 1,
      timestamp: '2024-01-15 10:30:00',
      action: 'updated',
      name: 'Target Settings',
      user: 'Admin User',
      description: 'Updated target calculation settings'
    },
    {
      id: 2,
      timestamp: '2023-12-20 14:20:00',
      action: 'updated',
      name: 'Financial Year',
      user: 'System Admin',
      description: 'Changed financial year start date'
    }
  ]);

  // Activity log columns
  const activityColumns = [
    { field: 'timestamp', headerName: 'Date & Time', flex: 1, minWidth: 150 },
    { field: 'action', headerName: 'Action', flex: 0.7, minWidth: 100 },
    { field: 'name', headerName: 'Name', flex: 1, minWidth: 120 },
    { field: 'user', headerName: 'User', flex: 1, minWidth: 120 },
    { field: 'description', headerName: 'Description', flex: 2, minWidth: 200 }
  ];

  // Quality submission options with capitalized first letters
  const qualitySubmissionOptions = [
    { value: 'offer_accepted', label: 'Offer Accepted' },
    { value: 'offer_rejected', label: 'Offer Rejected' },
    { value: 'placed', label: 'Placed' },
    { value: 'no_show', label: 'No Show' },
    { value: 'archived', label: 'Archived' },
    { value: 'keep_hold', label: 'Keep Hold' },
    { value: 'negotiation', label: 'Negotiation' },
    { value: 'waiting_for_evaluation', label: 'Waiting for Evaluation' },
    { value: 'internal_interview', label: 'Internal Interview' },
    { value: 'selected_internally', label: 'Selected Internally' },
    { value: 'rejected_internally', label: 'Rejected Internally' },
    { value: 'withdrawn', label: 'Withdrawn' },
    { value: 'internal_disqualified', label: 'Internal Disqualified' },
    { value: 'offer_sent', label: 'Offer Sent' }
  ];

  // Initialize form with useForm hook
  const methods = useForm({
    defaultValues: {
      financial_year_start: '',
      financial_year_end: '',
      employee_joined_calculation: 'prorate',
      role_change_calculation: 'prorate',
      targets_created_calculation: 'full_rate',
      auto_assign_target: false,
      round_prorate_calculation: 'down',
      quality_submission: '',
      weekend_days: {
        sun: false,
        mon: false,
        tue: false,
        wed: false,
        thu: false,
        fri: false,
        sat: false
      },
      auto_recur: false
    }
  });

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = methods;

  // Calculate financial year end based on start date
  useEffect(() => {
    const startDate = watch('financial_year_start');
    if (startDate) {
      try {
        const date = new Date(startDate);
        date.setFullYear(date.getFullYear() + 1);
        date.setDate(date.getDate() - 1);

        // Format the date as DD/Month/YYYY
        const months = [
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December'
        ];
        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        setValue('financial_year_end', `${day}/${month}/${year}`);
      } catch (error) {
        console.error('Error calculating end date:', error);
      }
    }
  }, [watch('financial_year_start'), setValue]);

  const onSubmit = (data) => {
    console.log('Form data:', data);

    // Add activity log for save action
    const newLog = {
      id: activityLogs.length + 1,
      timestamp: new Date()
        .toLocaleString('en-GB', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
        .replace(',', ''),
      action: 'updated',
      name: 'Target Settings',
      user: 'Current User',
      description: 'Updated target settings configuration'
    };

    setActivityLogs((prev) => [newLog, ...prev]);

    // Show success message
    alert('Target settings saved successfully!');
  };

  return (
    <MainCard
      title="Target Settings"
      sx={{ borderRadius: '0%', backgroundColor: 'white' }}
      secondary={
        <Box>
          {showActivities ? (
            <Button
              variant="outlined"
              size="small"
              onClick={() => setShowActivities(false)}
              sx={{ textTransform: 'none' }}
            >
              Back
            </Button>
          ) : (
            <Button
              variant="outlined"
              size="small"
              onClick={() => setShowActivities(true)}
              sx={{ textTransform: 'none' }}
            >
              Activities
            </Button>
          )}
        </Box>
      }
    >
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          {showActivities ? (
            <>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Activity Log</Typography>
                {/* <Button onClick={() => setShowActivities(false)} variant="outlined" size="small">
                  Back
                </Button> */}
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ height: 400, width: '100%' }}>
                    <CustomDataGrid
                      rows={activityLogs}
                      columns={activityColumns}
                      paginationModel={paginationModel}
                      onPaginationModelChange={setPaginationModel}
                      pageSizeOptions={[5, 10, 25]}
                      rowCount={activityLogs.length}
                    />
                  </Box>
                </Grid>
              </Grid>
            </>
          ) : (
            <>
              {/* Financial Year Section - Optimized for tablet and laptop */}
              <MainCard title="How do you start calculating targets in your company?" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      Financial Year Starts
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Stack
                      direction={{ xs: 'column', sm: 'row' }}
                      spacing={{ xs: 1, sm: 2 }}
                      alignItems={{ xs: 'flex-start', sm: 'center' }}
                      sx={{ width: '100%' }}
                    >
                      <Box
                        sx={{
                          width: {
                            xs: '100%',
                            sm: isTablet ? '180px' : '220px',
                            md: '220px'
                          }
                        }}
                      >
                        <Controller
                          name="financial_year_start"
                          control={control}
                          rules={{ required: 'Financial year start date is required' }}
                          render={({ field, fieldState: { error } }) => (
                            <>
                              <CustomDateOfBirth
                                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                                {...field}
                                placeholder="Select Date"
                                fullWidth={isTablet}
                              />
                              {error && <CustomFormHelperText>{error.message}</CustomFormHelperText>}
                            </>
                          )}
                        />
                      </Box>
                      <CustomInputLabel sx={{ fontWeight: 500 }}>Ends</CustomInputLabel>
                      <CustomInputLabel
                        sx={{
                          fontWeight: 400,
                          color: theme.palette.text.secondary
                        }}
                      >
                        {watch('financial_year_end') || 'Not set'}
                      </CustomInputLabel>
                    </Stack>
                  </Grid>
                </Grid>
              </MainCard>

              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      How do you calculate targets for employees who joined after the start of the financial year?
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="employee_joined_calculation"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup
                          {...field}
                          row={!isTablet}
                          sx={{
                            flexDirection: isTablet ? 'column' : 'row',
                            alignItems: isTablet ? 'flex-start' : 'center',
                            '& .MuiFormControlLabel-root': {
                              marginRight: isTablet ? 0 : 2
                            }
                          }}
                        >
                          <FormControlLabel
                            value="prorate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Prorate</CustomInputLabel>}
                          />
                          <FormControlLabel
                            value="full_rate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Full Rate</CustomInputLabel>}
                          />
                        </RadioGroup>
                      )}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Role Change Calculation */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      In case an employee's role changes, how do you want to calculate the targets?
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="role_change_calculation"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup
                          {...field}
                          row={!isTablet}
                          sx={{
                            flexDirection: isTablet ? 'column' : 'row',
                            alignItems: isTablet ? 'flex-start' : 'center',
                            '& .MuiFormControlLabel-root': {
                              marginRight: isTablet ? 0 : 2
                            }
                          }}
                        >
                          <FormControlLabel
                            value="prorate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Prorate</CustomInputLabel>}
                          />
                          <FormControlLabel
                            value="full_rate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Full Rate</CustomInputLabel>}
                          />
                        </RadioGroup>
                      )}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Targets Created Calculation */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      How do you want to calculate the targets that are created after the start of the financial year?
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="targets_created_calculation"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup
                          {...field}
                          row={!isTablet}
                          sx={{
                            flexDirection: isTablet ? 'column' : 'row',
                            alignItems: isTablet ? 'flex-start' : 'center',
                            '& .MuiFormControlLabel-root': {
                              marginRight: isTablet ? 0 : 2
                            }
                          }}
                        >
                          <FormControlLabel
                            value="prorate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Prorate (based on effective date)</CustomInputLabel>}
                          />
                          <FormControlLabel
                            value="full_rate"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Full Rate</CustomInputLabel>}
                          />
                        </RadioGroup>
                      )}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Auto Assign Target */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      If a new member is added to the role, automatically assign the same Target.
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="auto_assign_target"
                      control={control}
                      render={({ field: { value, onChange } }) => <Switch checked={value} onChange={onChange} color="primary" />}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Rounding Options */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      How do you want to round the prorate calculations?
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="round_prorate_calculation"
                      control={control}
                      render={({ field }) => (
                        <RadioGroup
                          {...field}
                          row={!isTablet}
                          sx={{
                            flexDirection: isTablet ? 'column' : 'row',
                            alignItems: isTablet ? 'flex-start' : 'center',
                            '& .MuiFormControlLabel-root': {
                              marginRight: isTablet ? 0 : 2
                            }
                          }}
                        >
                          <FormControlLabel
                            value="down"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Rounded Down</CustomInputLabel>}
                          />
                          <FormControlLabel
                            value="up"
                            control={<Radio color="primary" />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Rounded Up</CustomInputLabel>}
                          />
                        </RadioGroup>
                      )}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Quality Submission */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      What do you consider as a quality submission?
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Box
                      sx={{
                        width: {
                          xs: '100%',
                          sm: isTablet ? '100%' : '50%',
                          md: '50%'
                        }
                      }}
                    >
                      <Controller
                        name="quality_submission"
                        control={control}
                        render={({ field }) => (
                          <CustomDropdownField
                            {...field}
                            placeholder="Select option"
                            options={qualitySubmissionOptions}
                            searchable={true}
                            sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                          />
                        )}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </MainCard>

              {/* Weekend Configuration */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      Weekend Configuration
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Box
                      sx={{
                        display: 'flex',
                        gap: { xs: 1, sm: 2 },
                        flexWrap: 'wrap',
                        flexDirection: isTablet ? 'column' : 'row'
                      }}
                    >
                      <Controller
                        name="weekend_days.sun"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Sun</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.mon"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Mon</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.tue"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Tue</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.wed"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Wed</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.thu"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Thu</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.fri"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Fri</CustomInputLabel>}
                          />
                        )}
                      />
                      <Controller
                        name="weekend_days.sat"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <FormControlLabel
                            control={<Checkbox checked={value} onChange={onChange} />}
                            label={<CustomInputLabel sx={{ fontWeight: 400 }}>Sat</CustomInputLabel>}
                          />
                        )}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </MainCard>

              {/* Auto Recur */}
              <MainCard title="" sx={{ mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={4} lg={4} xl={6}>
                    <CustomInputLabel
                      sx={{
                        whiteSpace: 'normal',
                        textOverflow: 'unset',
                        overflow: 'visible',
                        fontWeight: 500
                      }}
                    >
                      Automatically recur this configuration for the next financial year
                    </CustomInputLabel>
                  </Grid>
                  <Grid item xs={12} sm={6} md={8} lg={8} xl={6}>
                    <Controller
                      name="auto_recur"
                      control={control}
                      render={({ field: { value, onChange } }) => <Switch checked={value} onChange={onChange} color="primary" />}
                    />
                  </Grid>
                </Grid>
              </MainCard>

              {/* Submit Button */}
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button type="submit" variant="contained" color="primary" sx={{ textTransform: 'none' }}>
                  Save
                </Button>
              </Box>
            </>
          )}
        </form>
      </FormProvider>
    </MainCard>
  );
}
export default TargetSettings;
