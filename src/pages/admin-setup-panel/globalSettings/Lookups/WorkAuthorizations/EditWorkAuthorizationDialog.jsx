import React, { useEffect } from 'react';
import { Drawer, Typo<PERSON>, Box, Divider, IconButton, Button, Grid, Stack, FormControlLabel, Checkbox } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomNameField from 'components/custom-components/CustomNameField';
import { useForm, Controller } from 'react-hook-form';

const EditWorkAuthorizationDialog = ({ open, onClose, onSave, workAuth }) => {
  const { handleSubmit, control, reset } = useForm({
    defaultValues: { work_authorization: '' }
  });

  useEffect(() => {
    if (workAuth) {
      reset({ work_authorization: workAuth.work_authorization });
    }
  }, [workAuth, reset]);

  const handleFormClose = () => {
    reset();
    onClose();
  };

  const onSubmit = (data) => {
    onSave({ ...workAuth, ...data });
    handleFormClose();
  };

  return (
    <Drawer anchor="right" open={open} onClose={handleFormClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, zIndex: 1 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h5">Edit Work Authorization</Typography>
            <IconButton onClick={handleFormClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <Divider />
        {/* Form */}
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ p: 3, flexGrow: 1, overflowY: 'auto', bgcolor: 'rgba(248, 249, 250, 1)' }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="work_authorization" required>
                  Work Authorization
                </CustomInputLabel>
                <Controller
                  name="work_authorization"
                  control={control}
                  rules={{ required: 'Work Authorization is required' }}
                  render={({ field, fieldState }) => (
                    <CustomNameField
                      {...field}
                      control={control}
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      placeholder="Enter work authorization"
                sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                    />
                  )}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="is_default"
                control={control}
                render={({ field }) => (
                  <FormControlLabel control={<Checkbox {...field} checked={!!field.value} color="primary" />} label={<>Is Default</>} />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        {/* Footer */}
        <Divider />
        <Box
          sx={{
            p: 2,
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
            position: 'sticky',
            bottom: 0,
            bgcolor: 'background.paper',
            zIndex: 1
          }}
        >
          <Button onClick={handleFormClose} size="small" variant="outlined">
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default EditWorkAuthorizationDialog;
