import React, { useState } from 'react';
import { Box, Button, Grid, IconButton, Menu, MenuItem } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import Divider from '@mui/material/Divider';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';
import AddBackgroundFeedbackDialog from './AddBackgroundFeedbackDialog';
import EditBackgroundFeedbackDialog from './EditBackgroundFeedbackDialog';

function BackgroundFeedback() {
  const [feedbackList, setFeedbackList] = useState([
    {
      id: 1,
      feedback: 'Good Communication',
      created_by: 'Admin',
      modified_by: 'Admin',
      last_modified: '2023-01-01'
    }
  ]);

  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState(null);

  // Actions menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuRowId, setMenuRowId] = useState(null);

  const handleAddDialogOpen = () => setOpenAddDialog(true);
  const handleAddDialogClose = () => setOpenAddDialog(false);

  const handleEditDialogOpen = (row) => {
    setSelectedFeedback(row);
    setOpenEditDialog(true);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
    setSelectedFeedback(null);
  };

  const handleAddFeedback = (data) => {
    setFeedbackList((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        feedback: data.feedback,
        created_by: 'User',
        modified_by: 'User',
        last_modified: new Date().toISOString().slice(0, 10)
      }
    ]);
    handleAddDialogClose();
  };

  const handleEditFeedback = (data) => {
    setFeedbackList((prev) =>
      prev.map((item) =>
        item.id === selectedFeedback.id
          ? { ...item, feedback: data.feedback, modified_by: 'User', last_modified: new Date().toISOString().slice(0, 10) }
          : item
      )
    );
    handleEditDialogClose();
  };

  const handleMenuOpen = (event, row) => {
    setAnchorEl(event.currentTarget);
    setMenuRowId(row.id);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuRowId(null);
  };

  const handleDelete = (row) => {
    setFeedbackList((prev) => prev.filter((item) => item.id !== row.id));
    handleMenuClose();
  };

  const columns = [
    { field: 'feedback', headerName: 'BACKGROUND FEEDBACK', flex: 1, minWidth: 200 },
    { field: 'created_by', headerName: 'CREATED BY', flex: 1, minWidth: 120 },
    { field: 'modified_by', headerName: 'MODIFIED BY', flex: 1, minWidth: 120 },
    { field: 'last_modified', headerName: 'LAST MODIFIED', flex: 1, minWidth: 150 },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <IconButton onClick={(e) => handleMenuOpen(e, params.row)}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={menuRowId === params.row.id && Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            <MenuItem
              onClick={() => {
                handleEditDialogOpen(params.row);
                handleMenuClose();
              }}
            >
              Edit
            </MenuItem>
            <MenuItem onClick={() => handleDelete(params.row)}>Delete</MenuItem>
          </Menu>
        </>
      )
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Box display="flex" alignItems="center" gap={1}>
            <Button variant="outlined" size="small">
              Activities
            </Button>
            <Button variant="contained" size="small" color="primary" onClick={handleAddDialogOpen}>
              + Add
            </Button>
          </Box>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid
              rows={feedbackList}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={feedbackList.length}
            />
          </Box>
        </Grid>
      </Grid>
      <AddBackgroundFeedbackDialog open={openAddDialog} onClose={handleAddDialogClose} onSave={handleAddFeedback} />
      <EditBackgroundFeedbackDialog
        open={openEditDialog}
        onClose={handleEditDialogClose}
        onSave={handleEditFeedback}
        feedback={selectedFeedback}
      />
    </>
  );
}

export default BackgroundFeedback;
