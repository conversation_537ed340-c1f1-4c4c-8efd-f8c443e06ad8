import React, { useState } from 'react';
import { Box, Tabs, Tab } from '@mui/material';
import BackgroundFeedback from './BackgroundFeedback';
import Languages from './Languages';
import PayFrequencyTypes from './PayFrequencyTypes';
import PaymentTerms from './PaymentTerms';
import TaxTerms from './TaxTerms';
import WorkAuthorizations from './WorkAuthorizations';
import MainCard from 'components/MainCard';
import { borderRadius } from '@mui/system';

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

function Lookups() {
  const [value, setValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <MainCard sx={{borderRadius:"0"}}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange} aria-label="employment settings tabs" variant="scrollable" scrollButtons="auto" sx={{
          mt:-2,
          ml: -2,
          alignItems: 'flex-start',
          justifyContent: 'flex-start',
          borderBottom: 0.2,
          borderColor: 'divider'
        }}>
          <Tab label="Background Feedback" {...a11yProps(0)} />
          <Tab label="Languages" {...a11yProps(1)} />
          <Tab label="Pay Frequency Types" {...a11yProps(2)} />
          <Tab label="Payment Terms" {...a11yProps(3)} />
          <Tab label="Tax Terms" {...a11yProps(4)} />
          <Tab label="Work Authorizations" {...a11yProps(5)} />
        </Tabs>
      </Box>
      <Box>
        {value === 0 && <BackgroundFeedback />}
        {value === 1 && <Languages />}
        {value === 2 && <PayFrequencyTypes />}
        {value === 3 && <PaymentTerms />}
        {value === 4 && <TaxTerms />}
        {value === 5 && <WorkAuthorizations />}
      </Box>
    </MainCard>
  );
}

export default Lookups;
