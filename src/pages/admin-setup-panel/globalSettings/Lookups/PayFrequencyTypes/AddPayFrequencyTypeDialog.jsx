import React, { useEffect } from 'react';
import { Drawer, Box, Divider, IconButton, Button, Grid, Stack, Typography, InputAdornment, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useForm } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';

function AddPayFrequencyTypeDialog({ open, onClose, onSave }) {
  const { control, handleSubmit, reset } = useForm();

  const handleSave = (data) => {
    onSave(data);
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Add Pay Frequency Type</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <Divider />
        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="pay_frequency_type" required>Pay Frequency Type</CustomInputLabel>
                <CustomNameField
                  name="pay_frequency_type"
                  control={control}
                  placeholder="Enter Pay Frequency Type"
                  rules={{ required: 'Pay Frequency Type is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="abbreviated_text">
                  Abbreviated Text
                </CustomInputLabel>
                  {/* <Tooltip title="Short form or code for the pay frequency type.">
                    <InputAdornment position="end" sx={{ ml: 0.5, verticalAlign: 'middle', cursor: 'pointer' }}>
                      <InfoOutlinedIcon fontSize="small" />
                    </InputAdornment>
                  </Tooltip> */}
                <CustomNameField
                  name="abbreviated_text"
                  control={control}
                  placeholder="Enter Abbreviated Text"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
          </Grid>
        </Box>
        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default AddPayFrequencyTypeDialog; 