import React, { useEffect } from 'react';
import {
  Drawer,
  Typo<PERSON>,
  Box,
  Divider,
  IconButton,
  Button,
  Grid,
  Stack,
  InputAdornment,
  Tooltip,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useForm, Controller } from 'react-hook-form';
import CustomNameField from 'components/custom-components/CustomNameField';
import CustomInputLabel from 'components/custom-components/CustomInputLabel';
import CustomDropdownField from 'components/custom-components/CustomDropdownField';

const employmentTypeOptions = [
  { value: 'Consultant', label: 'Consultant' },
  { value: 'Contractor', label: 'Contractor' },
  { value: 'Employee', label: 'Employee' }
];

function EditTaxTermDialog({ open, onClose, onSave, taxTerm }) {
  const { control, handleSubmit, reset, setValue } = useForm();

  useEffect(() => {
    if (open && taxTerm) {
      setValue('tax_term', taxTerm.tax_term || '');
      setValue('employment_type', taxTerm.employment_type || '');
      setValue('abbreviated_text', taxTerm.abbreviated_text || '');
      setValue('is_default', !!taxTerm.is_default);
    }
  }, [open, taxTerm, setValue]);

  const handleSave = (data) => {
    onSave({ ...taxTerm, ...data });
    reset();
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <Drawer anchor="right" open={open} onClose={onClose}>
      <Box sx={{ width: { xs: '100%', sm: 400 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Sticky Header */}
        <Box sx={{ p: 2, position: 'sticky', top: 0, bgcolor: 'background.paper', zIndex: 1 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5">Edit Tax Term</Typography>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
        <Divider />
        {/* Form */}
        <Box component="form" onSubmit={handleSubmit(handleSave)} sx={{ p: 3, flexGrow: 1, overflowY: 'auto' }}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="tax_term" required>
                  Tax Terms
                </CustomInputLabel>
                <CustomNameField
                  name="tax_term"
                  control={control}
                  placeholder="Enter Tax Terms"
                  rules={{ required: 'Tax Terms is required' }}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="employment_type">Employment Type</CustomInputLabel>
                <CustomDropdownField
                  name="employment_type"
                  control={control}
                  placeholder="Select"
                  options={employmentTypeOptions}
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Stack spacing={1}>
                <CustomInputLabel htmlFor="abbreviated_text">Abbreviated Text</CustomInputLabel>
                <CustomNameField
                  name="abbreviated_text"
                  control={control}
                  placeholder="Enter Abbreviated Text"
                  sx={{ backgroundColor: 'rgba(248, 249, 250, 1)' }}
                />
              </Stack>
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="is_default"
                control={control}
                render={({ field }) => (
                  <FormControlLabel control={<Checkbox {...field} checked={!!field.value} color="primary" />} label={<>Is Default</>} />
                )}
              />
            </Grid>
          </Grid>
        </Box>
        {/* Footer */}
        <Divider />
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            onClick={() => {
              reset();
              onClose();
            }}
            size="small"
            variant="outlined"
          >
            Cancel
          </Button>
          <Button type="submit" size="small" variant="contained" color="primary" onClick={handleSubmit(handleSave)}>
            Save
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}

export default EditTaxTermDialog;
