import React, { useState } from 'react';
import { Box, Button, Grid, IconButton, Menu, MenuItem } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CustomCardHeader from 'components/custom-components/CustomCardHeader';
import EditTaxTermDialog from './EditTaxTermDialog';
import Divider from '@mui/material/Divider';
import CustomDataGrid from 'components/custom-components/CustomDataGrid';

function TaxTerms() {
  const [taxTermList, setTaxTermList] = useState([
    {
      id: 1,
      tax_term: 'Seasonal',
      employment_type: 'Consultant',
      abbreviated_text: 'S',
      is_default: false,
      created_by: 'Admin',
      modified_by: 'Admin',
      last_modified: '2023-01-01'
    }
  ]);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedTaxTerm, setSelectedTaxTerm] = useState(null);

  // Actions menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuRowId, setMenuRowId] = useState(null);

  const handleEditDialogOpen = (row) => {
    setSelectedTaxTerm(row);
    setOpenEditDialog(true);
  };
  const handleEditDialogClose = () => {
    setOpenEditDialog(false);
    setSelectedTaxTerm(null);
  };

  const handleEditTaxTerm = (data) => {
    setTaxTermList((prev) =>
      prev.map((item) =>
        item.id === selectedTaxTerm.id
          ? { ...item, ...data, modified_by: 'User', last_modified: new Date().toISOString().slice(0, 10) }
          : item
      )
    );
    handleEditDialogClose();
  };

  const handleMenuOpen = (event, row) => {
    setAnchorEl(event.currentTarget);
    setMenuRowId(row.id);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuRowId(null);
  };

  const columns = [
    { field: 'tax_term', headerName: 'TAX TERMS', flex: 1, minWidth: 200 },
    { field: 'abbreviated_text', headerName: 'ABBREVIATED TEXT', flex: 1, minWidth: 150 },
    { field: 'created_by', headerName: 'CREATED BY', flex: 1, minWidth: 120 },
    { field: 'modified_by', headerName: 'MODIFIED BY', flex: 1, minWidth: 120 },
    { field: 'last_modified', headerName: 'LAST MODIFIED', flex: 1, minWidth: 150 },
    {
      field: 'actions',
      headerName: 'ACTIONS',
      flex: 0.5,
      minWidth: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <IconButton onClick={(e) => handleMenuOpen(e, params.row)}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={menuRowId === params.row.id && Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          >
            <MenuItem
              onClick={() => {
                handleEditDialogOpen(params.row);
                handleMenuClose();
              }}
            >
              Edit
            </MenuItem>
          </Menu>
        </>
      )
    }
  ];

  const [paginationModel, setPaginationModel] = useState({ pageSize: 10, page: 0 });

  return (
    <>
      <CustomCardHeader
        secondary={
          <Button variant="contained" size="small" color="primary">
            Save
          </Button>
        }
      />
      <Divider sx={{ mb: 1 }} />
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ height: 300, width: '100%' }}>
            <CustomDataGrid 
              rows={taxTermList}
              columns={columns}
              paginationModel={paginationModel}
              onPaginationModelChange={setPaginationModel}
              rowCount={taxTermList.length}
            />
          </Box>
        </Grid>
      </Grid>
      <EditTaxTermDialog
        open={openEditDialog}
        onClose={handleEditDialogClose}
        onSave={handleEditTaxTerm}
        taxTerm={selectedTaxTerm}
      />
    </>
  );
}

export default TaxTerms; 