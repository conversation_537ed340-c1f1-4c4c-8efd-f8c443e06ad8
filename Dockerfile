# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* yarn.lock ./
RUN npm ci --omit=optional

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY package.json package-lock.json* yarn.lock ./
# Install all dependencies (including devDependencies) for build
RUN npm ci
COPY . .

# Accept build arguments for CI/CD compatibility
ARG ENV_FILE=.env.production
ARG BUILD_SCRIPT=build:prod
ARG BUILD_ENV=production

# Set environment variables for build
ENV NODE_ENV production
ENV VITE_APP_PORT=3000

# Generate runtime configuration (use BUILD_ENV if provided, otherwise derive from BUILD_SCRIPT)
RUN if [ "$BUILD_ENV" != "production" ]; then \
      node scripts/generate-runtime-config.js ${BUILD_ENV}; \
    else \
      node scripts/generate-runtime-config.js production; \
    fi

# Build the application using the specified build script
RUN npm run ${BUILD_SCRIPT}

# Production image, copy all the files and run with nginx
FROM nginx:alpine AS runner

# Copy built assets from builder
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create a non-root user to run nginx
RUN getent group nginx || addgroup -g 1001 -S nginx && \
    id -u nginx 2>/dev/null || adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Change ownership of nginx directories to the nginx user
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# Switch to nginx user for security
USER nginx

EXPOSE 80

# Health check to ensure the application is running
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
 