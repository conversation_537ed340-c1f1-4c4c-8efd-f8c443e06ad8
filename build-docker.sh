#!/bin/bash

# ATS Frontend Application - Docker Build Script
# 
# This script simplifies building Docker images for different environments.
# 
# Usage:
#   ./build-docker.sh [environment] [tag]
# 
# Examples:
#   ./build-docker.sh production latest
#   ./build-docker.sh development staging
#   ./build-docker.sh beta v1.0.0

set -e

# Default values
ENVIRONMENT=${1:-production}
TAG=${2:-latest}
IMAGE_NAME="ats-frontend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(production|development|beta)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: production, development, beta"
    exit 1
fi

print_status "Building ATS Frontend Application Docker image..."
print_status "Environment: $ENVIRONMENT"
print_status "Tag: $TAG"
print_status "Image: $IMAGE_NAME:$TAG"

# Generate runtime configuration
print_status "Generating runtime configuration for $ENVIRONMENT..."
node scripts/generate-runtime-config.js $ENVIRONMENT

if [ $? -eq 0 ]; then
    print_success "Runtime configuration generated successfully"
else
    print_error "Failed to generate runtime configuration"
    exit 1
fi

# Build Docker image
print_status "Building Docker image..."
docker build \
    --build-arg BUILD_ENV=$ENVIRONMENT \
    --tag $IMAGE_NAME:$TAG \
    --tag $IMAGE_NAME:$ENVIRONMENT \
    .

if [ $? -eq 0 ]; then
    print_success "Docker image built successfully!"
    print_status "Image: $IMAGE_NAME:$TAG"
    print_status "Image: $IMAGE_NAME:$ENVIRONMENT"
    
    # Show image info
    print_status "Image details:"
    docker images $IMAGE_NAME:$TAG --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    print_status "To run the container:"
    echo "  docker run -p 3000:80 $IMAGE_NAME:$TAG"
    echo "  docker run -p 3000:80 $IMAGE_NAME:$ENVIRONMENT"
    
else
    print_error "Failed to build Docker image"
    exit 1
fi

print_success "Build completed successfully!" 